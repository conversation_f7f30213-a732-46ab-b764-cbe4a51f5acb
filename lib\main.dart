import 'package:flutter/material.dart';
import 'dart:ui';
import 'screens/splash_screen.dart';
import 'package:provider/provider.dart';
import 'providers/settings_provider.dart';
import 'providers/hadith_provider.dart';
import 'providers/quran_provider.dart';
import 'providers/azkar_provider.dart';
import 'providers/audio_provider.dart';
import 'theme/app_themes.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // معالجة الأخطاء العامة
  FlutterError.onError = (details) {
    FlutterError.presentError(details);
    // يمكن إضافة تسجيل الأخطاء هنا
  };

  PlatformDispatcher.instance.onError = (error, stack) {
    // معالجة الأخطاء غير المتوقعة
    debugPrint('خطأ غير متوقع: $error');
    debugPrint('Stack trace: $stack');
    return true;
  };

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => SettingsProvider()),
        ChangeNotifierProvider(create: (_) => HadithProvider()),
        ChangeNotifierProvider(create: (_) => QuranProvider()),
        ChangeNotifierProvider(create: (_) => AzkarProvider()),
        ChangeNotifierProvider(create: (_) => AudioProvider()),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<SettingsProvider>(
      builder: (context, settings, child) {
        return MaterialApp(
          title: 'قرآني - تطبيق القرآن الكريم',
          debugShowCheckedModeBanner: false,

          // استخدام نظام الثيمات الجديد
          theme: settings.isDarkMode
            ? AppThemes.createDarkTheme(settings.fontSize)
            : AppThemes.createLightTheme(settings.themeColor, settings.fontSize),

          // دعم اللغة العربية
          locale: const Locale('ar', 'SA'),

          home: const SplashScreen(),

          // إعدادات إضافية للتطبيق
          builder: (context, child) {
            return MediaQuery(
              data: MediaQuery.of(context).copyWith(
                // تعديل حجم النص حسب إعدادات إمكانية الوصول
                textScaler: TextScaler.linear(
                  settings.accessibilityMode ? 1.2 : 1.0,
                ),
              ),
              child: child!,
            );
          },
        );
      },
    );
  }
}


