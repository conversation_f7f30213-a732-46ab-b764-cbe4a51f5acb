import 'package:flutter/material.dart';
import 'dart:ui';
import 'screens/splash_screen.dart';
import 'package:provider/provider.dart';
import 'providers/settings_provider.dart';
import 'providers/hadith_provider.dart';
import 'providers/quran_provider.dart';
import 'providers/azkar_provider.dart';
import 'providers/audio_provider.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // معالجة الأخطاء العامة
  FlutterError.onError = (details) {
    FlutterError.presentError(details);
    // يمكن إضافة تسجيل الأخطاء هنا
  };
  
  PlatformDispatcher.instance.onError = (error, stack) {
    // معالجة الأخطاء غير المتوقعة
    debugPrint('خطأ غير متوقع: $error');
    debugPrint('Stack trace: $stack');
    return true;
  };
  
  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => SettingsProvider()),
        ChangeNotifierProvider(create: (_) => HadithProvider()),
        ChangeNotifierProvider(create: (_) => QuranProvider()),
        ChangeNotifierProvider(create: (_) => AzkarProvider()),
        ChangeNotifierProvider(create: (_) => AudioProvider()),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Quraan App',
      theme: AppTheme.lightTheme,
      home: const SplashScreen(),
    );
  }
}

class AppTheme {
  static ThemeData get lightTheme {
    const primaryColor = Color(0xFF2E7D32);
    const surfaceColor = Color(0xFFFAFAFA);

    return ThemeData(
      primaryColor: primaryColor,
      colorScheme: const ColorScheme.light(
        primary: primaryColor,
        secondary: Color(0xFF81C784),
      ),
      scaffoldBackgroundColor: surfaceColor,
      appBarTheme: const AppBarTheme(
        color: primaryColor,
        elevation: 0,
      ),
    );
  }
}
