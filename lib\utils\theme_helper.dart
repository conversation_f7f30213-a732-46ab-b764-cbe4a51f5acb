import 'package:flutter/material.dart';

class ThemeHelper {
  // استبدال withOpacity بـ withValues
  static Color getColorWithOpacity(Color color, double opacity) {
    return color.withValues(alpha: (opacity * 255).round());
  }
  
  // إضافة طرق مساعدة أخرى للألوان
  static Color darken(Color color, double amount) {
    assert(amount >= 0 && amount <= 1);
    
    final hsl = HSLColor.fromColor(color);
    final hslDark = hsl.withLightness((hsl.lightness - amount).clamp(0.0, 1.0));
    
    return hslDark.toColor();
  }
  
  static Color lighten(Color color, double amount) {
    assert(amount >= 0 && amount <= 1);
    
    final hsl = HSLColor.fromColor(color);
    final hslLight = hsl.withLightness((hsl.lightness + amount).clamp(0.0, 1.0));
    
    return hslLight.toColor();
  }
}