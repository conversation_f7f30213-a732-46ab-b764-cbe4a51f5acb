import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/quran_provider.dart';
import '../providers/azkar_provider.dart';
import '../providers/hadith_provider.dart';
import '../providers/settings_provider.dart';
import '../widgets/quick_settings_widget.dart';
import '../widgets/quick_search_widget.dart';
import 'quran/mushaf_reader.dart';
import 'quran_audio_screen.dart';
import 'azkar/azkar_screen.dart';
import 'tasbih/tasbih_screen.dart';
import 'hadith/hadith_screen.dart';
import 'prayer_times/prayer_times_screen.dart';
import 'settings/settings_screen.dart';
import '../services/prayer_times_service.dart';
import '../widgets/audio_player_bar.dart';
import '../providers/audio_provider.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;

  late final List<Widget> _screens;

  @override
  void initState() {
    super.initState();
    _screens = [
      HomeTabScreen(onNavigateToTab: _navigateToTab),
      const QuranAudioScreen(),
      const MushafReader(),
      const AzkarScreen(),
      const TasbihScreen(),
      const HadithScreen(),
    ];
    _initializeProviders();
  }

  void _navigateToTab(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  Future<void> _initializeProviders() async {
    try {
      print('🏠 HomeScreen: Initializing providers...');

      final settingsProvider = Provider.of<SettingsProvider>(context, listen: false);
      final quranProvider = Provider.of<QuranProvider>(context, listen: false);
      final azkarProvider = Provider.of<AzkarProvider>(context, listen: false);
      final hadithProvider = Provider.of<HadithProvider>(context, listen: false);

      await settingsProvider.initialize();
      print('✅ HomeScreen: SettingsProvider initialized');

      await quranProvider.initialize();
      print('✅ HomeScreen: QuranProvider initialized');

      await azkarProvider.initialize();
      print('✅ HomeScreen: AzkarProvider initialized');

      await hadithProvider.initialize();
      print('✅ HomeScreen: HadithProvider initialized');

      print('🎉 HomeScreen: All providers initialized successfully');
    } catch (e) {
      print('❌ HomeScreen: Error initializing providers: $e');
      // Continue anyway to show the app
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // المحتوى الرئيسي
          Expanded(
            child: IndexedStack(
              index: _currentIndex,
              children: _screens,
            ),
          ),
          
          // شريط التشغيل
          Consumer<AudioProvider>(
            builder: (context, audioProvider, child) {
              if (audioProvider.isPlaying || audioProvider.isPaused) {
                return const AudioPlayerBar();
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        selectedItemColor: Theme.of(context).primaryColor,
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'القائمة',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.play_circle_filled),
            label: 'تشغيل القرآن',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.book),
            label: 'قراءة القرآن',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.favorite),
            label: 'الأذكار',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.radio_button_checked),
            label: 'السبحة',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.article),
            label: 'الأحاديث',
          ),
        ],
      ),
    );
  }
}

class HomeTabScreen extends StatefulWidget {
  final Function(int) onNavigateToTab;

  const HomeTabScreen({
    super.key,
    required this.onNavigateToTab,
  });

  @override
  State<HomeTabScreen> createState() => _HomeTabScreenState();
}

class _HomeTabScreenState extends State<HomeTabScreen> {
  final PrayerTimesService _prayerTimesService = PrayerTimesService();

  Map<String, TimeOfDay>? _prayerTimes;
  String? _nextPrayer;
  Duration? _timeUntilNext;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPrayerTimes();
    _startTimer();
  }

  void _startTimer() {
    Future.delayed(const Duration(minutes: 1), () {
      if (mounted) {
        _updateTimeUntilNext();
        _startTimer();
      }
    });
  }

  Future<void> _loadPrayerTimes() async {
    try {
      // Default location (Makkah)
      final prayerData = await _prayerTimesService.getPrayerTimes(
        latitude: 21.3891,
        longitude: 39.8579,
      );

      if (prayerData != null && mounted) {
        setState(() {
          _prayerTimes = _prayerTimesService.parsePrayerTimesArabic(prayerData);
          _nextPrayer = _prayerTimesService.getNextPrayer(_prayerTimes!);
          _timeUntilNext = _prayerTimesService.getTimeUntilNextPrayer(_prayerTimes!);
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _updateTimeUntilNext() {
    if (_prayerTimes != null && mounted) {
      setState(() {
        _nextPrayer = _prayerTimesService.getNextPrayer(_prayerTimes!);
        _timeUntilNext = _prayerTimesService.getTimeUntilNextPrayer(_prayerTimes!);
      });
    }
  }

  Future<void> _refreshData() async {
    try {
      await Future.wait([
        _loadPrayerTimes(),
        _refreshHadithOfTheDay(),
        _updateReadingProgress(),
      ]);
    } catch (e) {
      print('Error refreshing data: $e');
    }
  }

  Future<void> _refreshHadithOfTheDay() async {
    try {
      final hadithProvider = Provider.of<HadithProvider>(context, listen: false);
      // استخدام method موجود أو تجاهل إذا لم يكن موجود
      await hadithProvider.initialize();
    } catch (e) {
      print('Error refreshing hadith: $e');
    }
  }

  Future<void> _updateReadingProgress() async {
    try {
      final quranProvider = Provider.of<QuranProvider>(context, listen: false);
      // استخدام method موجود أو تجاهل إذا لم يكن موجود
      await quranProvider.initialize();
    } catch (e) {
      print('Error updating reading progress: $e');
    }
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;

    if (hours > 0) {
      return 'بعد $hours ساعة و $minutes دقيقة';
    } else {
      return 'بعد $minutes دقيقة';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('قرآني'),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const SettingsScreen()),
              );
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _refreshData,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // بطاقة الترحيب المحسنة
              _buildWelcomeCard(context),
              const SizedBox(height: 24),
              
              // البحث السريع
              const QuickSearchBar(),
              const SizedBox(height: 24),
              
              // الإجراءات السريعة المحسنة
              _buildQuickActions(context),
              const SizedBox(height: 24),
              
              // أوقات الصلاة (أولوية عالية)
              _buildPrayerTimesCard(context),
              const SizedBox(height: 24),
              
              // حديث اليوم
              _buildHadithOfTheDay(context),
              const SizedBox(height: 24),
              
              // تقدم القراءة والأذكار
              Row(
                children: [
                  Expanded(child: _buildReadingProgress(context)),
                  const SizedBox(width: 12),
                  Expanded(child: _buildAzkarProgress(context)),
                ],
              ),
              const SizedBox(height: 24),
              
              // الإعدادات السريعة في النهاية
              const QuickSettingsWidget(),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeCard(BuildContext context) {
    final now = DateTime.now();
    final hour = now.hour;
    String greeting;
    IconData greetingIcon;
    List<Color> gradientColors;
    
    if (hour < 12) {
      greeting = 'صباح الخير ☀️';
      greetingIcon = Icons.wb_sunny;
      gradientColors = [
        const Color(0xFF4FC3F7),
        const Color(0xFF29B6F6),
        const Color(0xFF03A9F4),
      ];
    } else if (hour < 17) {
      greeting = 'نهارك سعيد 🌤️';
      greetingIcon = Icons.wb_cloudy;
      gradientColors = [
        const Color(0xFF66BB6A),
        const Color(0xFF4CAF50),
        const Color(0xFF388E3C),
      ];
    } else {
      greeting = 'مساء الخير 🌙';
      greetingIcon = Icons.nightlight_round;
      gradientColors = [
        const Color(0xFF7E57C2),
        const Color(0xFF673AB7),
        const Color(0xFF512DA8),
      ];
    }

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          colors: gradientColors,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: gradientColors[1].withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Stack(
        children: [
          // خلفية زخرفية
          Positioned(
            top: -20,
            right: -20,
            child: Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white.withValues(alpha: 0.1),
              ),
            ),
          ),
          Positioned(
            bottom: -30,
            left: -30,
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white.withValues(alpha: 0.05),
              ),
            ),
          ),
          // المحتوى الرئيسي
          Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      greetingIcon,
                      color: Colors.white,
                      size: 28,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        greeting,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 26,
                          fontWeight: FontWeight.bold,
                          height: 1.2,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  'أهلاً وسهلاً بك في رحلتك الروحانية',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.9),
                    fontSize: 16,
                    height: 1.4,
                  ),
                ),
                const SizedBox(height: 20),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.15),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                  child: const Text(
                    '﴿وَقُلْ رَبِّ زِدْنِي عِلْمًا﴾',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      height: 1.6,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.dashboard_rounded,
              color: Theme.of(context).primaryColor,
              size: 24,
            ),
            const SizedBox(width: 8),
            const Text(
              'الإجراءات السريعة',
              style: TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        // الصف الأول - القرآن والأذكار
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                context,
                'قراءة القرآن',
                Icons.menu_book_rounded,
                const Color(0xFF4CAF50),
                const Color(0xFF66BB6A),
                '📖',
                () => widget.onNavigateToTab(1),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionCard(
                context,
                'الأذكار',
                Icons.favorite_rounded,
                const Color(0xFF2196F3),
                const Color(0xFF42A5F5),
                '🤲',
                () => widget.onNavigateToTab(2),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        // الصف الثاني - السبحة والأحاديث
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                context,
                'السبحة الإلكترونية',
                Icons.radio_button_checked_rounded,
                const Color(0xFFFF9800),
                const Color(0xFFFFB74D),
                '📿',
                () => widget.onNavigateToTab(3),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionCard(
                context,
                'الأحاديث النبوية',
                Icons.article_rounded,
                const Color(0xFF9C27B0),
                const Color(0xFFBA68C8),
                '📜',
                () => widget.onNavigateToTab(4),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        // الصف الثالث - أوقات الصلاة (عرض كامل)
        _buildActionCard(
          context,
          'أوقات الصلاة',
          Icons.access_time_rounded,
          const Color(0xFF00BCD4),
          const Color(0xFF26C6DA),
          '🕌',
          () => Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const PrayerTimesScreen()),
          ),
          isFullWidth: true,
        ),
      ],
    );
  }

  Widget _buildActionCard(
    BuildContext context,
    String title,
    IconData icon,
    Color primaryColor,
    Color secondaryColor,
    String emoji,
    VoidCallback onTap, {
    bool isFullWidth = false,
  }) {
    return Container(
      width: isFullWidth ? double.infinity : null,
      margin: const EdgeInsets.only(bottom: 4),
      child: Card(
        elevation: 4,
        shadowColor: primaryColor.withValues(alpha: 0.3),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: EdgeInsets.all(isFullWidth ? 20 : 16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: LinearGradient(
                colors: [
                  primaryColor.withValues(alpha: 0.1),
                  secondaryColor.withValues(alpha: 0.05),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: isFullWidth
                ? Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [primaryColor, secondaryColor],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: primaryColor.withValues(alpha: 0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Icon(
                          icon,
                          size: 28,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              title,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'اضغط للعرض',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                      Text(
                        emoji,
                        style: const TextStyle(fontSize: 24),
                      ),
                    ],
                  )
                : Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [primaryColor, secondaryColor],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: primaryColor.withValues(alpha: 0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Icon(
                          icon,
                          size: 28,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Text(
                        emoji,
                        style: const TextStyle(fontSize: 20),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
          ),
        ),
      ),
    );
  }

  Widget _buildPrayerTimesCard(BuildContext context) {
    final now = DateTime.now();
    final currentTime = '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';
    
    return Card(
      elevation: 6,
      shadowColor: const Color(0xFF00BCD4).withValues(alpha: 0.3),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: const LinearGradient(
            colors: [
              Color(0xFF00BCD4),
              Color(0xFF26C6DA),
              Color(0xFF4DD0E1),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // العنوان
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.mosque,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'أوقات الصلاة',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  Text(
                    currentTime,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              
              // محتوى أوقات الصلاة
              if (_isLoading)
                const Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              else if (_nextPrayer != null && _timeUntilNext != null) ...[
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.15),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          const Text(
                            '🕌',
                            style: TextStyle(fontSize: 24),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              'الصلاة القادمة: $_nextPrayer',
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          const Text(
                            '⏰',
                            style: TextStyle(fontSize: 20),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              _formatDuration(_timeUntilNext!),
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.white.withValues(alpha: 0.9),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ] else
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.15),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Row(
                    children: [
                      Text(
                        '📍',
                        style: TextStyle(fontSize: 20),
                      ),
                      SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'لا يمكن تحديد أوقات الصلاة',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              
              const SizedBox(height: 16),
              
              // زر عرض التفاصيل
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => const PrayerTimesScreen()),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: const Color(0xFF00BCD4),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    'عرض جميع أوقات الصلاة',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHadithOfTheDay(BuildContext context) {
    return Consumer<HadithProvider>(
      builder: (context, hadithProvider, child) {
        final hadithOfTheDay = hadithProvider.hadithOfTheDay;

        if (hadithOfTheDay == null) {
          return const SizedBox.shrink();
        }

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.article,
                      color: Theme.of(context).primaryColor,
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'حديث اليوم',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  hadithOfTheDay.hadith.arabicText,
                  style: const TextStyle(
                    fontSize: 16,
                    height: 1.8,
                  ),
                  textAlign: TextAlign.right,
                ),
                const SizedBox(height: 8),
                Text(
                  '- ${hadithOfTheDay.hadith.narrator}',
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildReadingProgress(BuildContext context) {
    return Consumer<QuranProvider>(
      builder: (context, quranProvider, child) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.book,
                      color: Theme.of(context).primaryColor,
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'آخر قراءة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  quranProvider.surahs.isNotEmpty && quranProvider.lastReadSurah > 0
                      ? 'سورة ${quranProvider.surahs.firstWhere((s) => s.number == quranProvider.lastReadSurah, orElse: () => quranProvider.surahs.first).name} - الآية ${quranProvider.lastReadAyah}'
                      : 'لم تبدأ القراءة بعد',
                  style: const TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 8),
                ElevatedButton(
                  onPressed: () => widget.onNavigateToTab(1),
                  child: const Text('متابعة القراءة'),
                ),
              ],
            ),
          ),
        );
      },
    );
  }



  Widget _buildAzkarProgress(BuildContext context) {
    return Consumer<AzkarProvider>(
      builder: (context, azkarProvider, child) {
        final progress = azkarProvider.getOverallProgress();
        final completedCount = (progress * 10).toInt(); // افتراض 10 أذكار
        final totalCount = 10;

        return Card(
          elevation: 4,
          shadowColor: const Color(0xFF2196F3).withValues(alpha: 0.3),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: LinearGradient(
                colors: [
                  const Color(0xFF2196F3).withValues(alpha: 0.1),
                  const Color(0xFF42A5F5).withValues(alpha: 0.05),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // العنوان مع الأيقونة
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [Color(0xFF2196F3), Color(0xFF42A5F5)],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(8),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0xFF2196F3).withValues(alpha: 0.3),
                              blurRadius: 6,
                              offset: const Offset(0, 3),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.favorite_rounded,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      const Expanded(
                        child: Text(
                          'تقدم الأذكار',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const Text(
                        '🤲',
                        style: TextStyle(fontSize: 20),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  
                  // شريط التقدم المحسن
                  Container(
                    height: 8,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      color: Colors.grey[300],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(4),
                      child: LinearProgressIndicator(
                        value: progress,
                        backgroundColor: Colors.transparent,
                        valueColor: const AlwaysStoppedAnimation<Color>(
                          Color(0xFF2196F3),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  
                  // معلومات التقدم
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '$completedCount من $totalCount',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xFF2196F3).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: const Color(0xFF2196F3).withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          '${(progress * 100).toInt()}%',
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF2196F3),
                          ),
                        ),
                      ),
                    ],
                  ),
                  
                  // رسالة تشجيعية
                  if (progress > 0.7)
                    Container(
                      margin: const EdgeInsets.only(top: 8),
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.green.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Colors.green.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: const Row(
                        children: [
                          Text('🌟', style: TextStyle(fontSize: 16)),
                          SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'أحسنت! تقدم ممتاز في الأذكار',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.green,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }




}
