import 'package:flutter/material.dart';

/// مساعد للتصميم المتجاوب
class ResponsiveHelper {
  /// الحصول على نوع الجهاز
  static DeviceType getDeviceType(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth < 600) {
      return DeviceType.mobile;
    } else if (screenWidth < 1200) {
      return DeviceType.tablet;
    } else {
      return DeviceType.desktop;
    }
  }

  /// التحقق من كون الجهاز هاتف
  static bool isMobile(BuildContext context) {
    return getDeviceType(context) == DeviceType.mobile;
  }

  /// التحقق من كون الجهاز تابلت
  static bool isTablet(BuildContext context) {
    return getDeviceType(context) == DeviceType.tablet;
  }

  /// التحقق من كون الجهاز سطح مكتب
  static bool isDesktop(BuildContext context) {
    return getDeviceType(context) == DeviceType.desktop;
  }

  /// الحصول على عدد الأعمدة المناسب للشبكة
  static int getGridColumns(BuildContext context, {int? mobileColumns, int? tabletColumns, int? desktopColumns}) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return mobileColumns ?? 1;
      case DeviceType.tablet:
        return tabletColumns ?? 2;
      case DeviceType.desktop:
        return desktopColumns ?? 3;
    }
  }

  /// الحصول على حجم الخط المتجاوب
  static double getResponsiveFontSize(BuildContext context, double baseFontSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth < 600) {
      return baseFontSize;
    } else if (screenWidth < 1200) {
      return baseFontSize * 1.1;
    } else {
      return baseFontSize * 1.2;
    }
  }

  /// الحصول على المسافات المتجاوبة
  static EdgeInsets getResponsivePadding(BuildContext context, {
    double? mobile,
    double? tablet,
    double? desktop,
  }) {
    final deviceType = getDeviceType(context);
    double padding;
    
    switch (deviceType) {
      case DeviceType.mobile:
        padding = mobile ?? 16.0;
        break;
      case DeviceType.tablet:
        padding = tablet ?? 24.0;
        break;
      case DeviceType.desktop:
        padding = desktop ?? 32.0;
        break;
    }
    
    return EdgeInsets.all(padding);
  }

  /// الحصول على عرض المحتوى الأقصى
  static double getMaxContentWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth < 600) {
      return screenWidth;
    } else if (screenWidth < 1200) {
      return 800;
    } else {
      return 1000;
    }
  }

  /// التحقق من الاتجاه الأفقي
  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  /// التحقق من الاتجاه العمودي
  static bool isPortrait(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.portrait;
  }

  /// الحصول على ارتفاع الشاشة الآمن
  static double getSafeAreaHeight(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return mediaQuery.size.height - 
           mediaQuery.padding.top - 
           mediaQuery.padding.bottom;
  }

  /// الحصول على عرض الشاشة الآمن
  static double getSafeAreaWidth(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return mediaQuery.size.width - 
           mediaQuery.padding.left - 
           mediaQuery.padding.right;
  }

  /// تخطيط متجاوب للقوائم
  static Widget buildResponsiveLayout({
    required BuildContext context,
    required Widget mobile,
    Widget? tablet,
    Widget? desktop,
  }) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return mobile;
      case DeviceType.tablet:
        return tablet ?? mobile;
      case DeviceType.desktop:
        return desktop ?? tablet ?? mobile;
    }
  }

  /// حساب حجم البطاقة المتجاوب
  static Size getCardSize(BuildContext context, {
    Size? mobileSize,
    Size? tabletSize,
    Size? desktopSize,
  }) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return mobileSize ?? const Size(double.infinity, 120);
      case DeviceType.tablet:
        return tabletSize ?? const Size(double.infinity, 140);
      case DeviceType.desktop:
        return desktopSize ?? const Size(double.infinity, 160);
    }
  }

  /// الحصول على نسبة العرض إلى الارتفاع للبطاقات
  static double getCardAspectRatio(BuildContext context) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return 16 / 9;
      case DeviceType.tablet:
        return 4 / 3;
      case DeviceType.desktop:
        return 3 / 2;
    }
  }

  /// تحديد حجم الأيقونة المتجاوب
  static double getIconSize(BuildContext context, {
    double? mobile,
    double? tablet,
    double? desktop,
  }) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return mobile ?? 24.0;
      case DeviceType.tablet:
        return tablet ?? 28.0;
      case DeviceType.desktop:
        return desktop ?? 32.0;
    }
  }

  /// تحديد ارتفاع شريط التطبيق المتجاوب
  static double getAppBarHeight(BuildContext context) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return kToolbarHeight;
      case DeviceType.tablet:
        return kToolbarHeight + 8;
      case DeviceType.desktop:
        return kToolbarHeight + 16;
    }
  }

  /// تحديد عدد العناصر في الصف للشبكة
  static int getCrossAxisCount(BuildContext context, double itemWidth) {
    final screenWidth = MediaQuery.of(context).size.width;
    final availableWidth = screenWidth - 32; // مع مراعاة المسافات
    return (availableWidth / itemWidth).floor().clamp(1, 6);
  }

  /// تحديد المسافة بين العناصر في الشبكة
  static double getGridSpacing(BuildContext context) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return 8.0;
      case DeviceType.tablet:
        return 12.0;
      case DeviceType.desktop:
        return 16.0;
    }
  }

  /// widget متجاوب للنصوص
  static Widget buildResponsiveText(
    String text, {
    required BuildContext context,
    TextStyle? baseStyle,
    double? scaleFactor,
  }) {
    final responsiveFontSize = getResponsiveFontSize(
      context, 
      baseStyle?.fontSize ?? 16.0,
    );
    
    return Text(
      text,
      style: (baseStyle ?? const TextStyle()).copyWith(
        fontSize: responsiveFontSize * (scaleFactor ?? 1.0),
      ),
    );
  }

  /// تحديد نوع التنقل المناسب
  static NavigationType getNavigationType(BuildContext context) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return NavigationType.bottomNavigation;
      case DeviceType.tablet:
        return isLandscape(context) 
            ? NavigationType.navigationRail 
            : NavigationType.bottomNavigation;
      case DeviceType.desktop:
        return NavigationType.navigationRail;
    }
  }

  /// تحديد عرض الدرج الجانبي
  static double getDrawerWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    return (screenWidth * 0.8).clamp(280.0, 400.0);
  }

  /// تحديد ما إذا كان يجب إظهار الدرج الجانبي
  static bool shouldShowDrawer(BuildContext context) {
    return !isDesktop(context);
  }

  /// تحديد ما إذا كان يجب إظهار شريط التنقل الجانبي
  static bool shouldShowNavigationRail(BuildContext context) {
    return isDesktop(context) || (isTablet(context) && isLandscape(context));
  }
}

/// أنواع الأجهزة
enum DeviceType {
  mobile,
  tablet,
  desktop,
}

/// أنواع التنقل
enum NavigationType {
  bottomNavigation,
  navigationRail,
  drawer,
}

/// مساعد للتحكم في الاتجاه
class OrientationHelper {
  /// قفل الاتجاه العمودي
  static void lockPortrait() {
    // يمكن إضافة الكود هنا لقفل الاتجاه
  }

  /// قفل الاتجاه الأفقي
  static void lockLandscape() {
    // يمكن إضافة الكود هنا لقفل الاتجاه
  }

  /// إلغاء قفل الاتجاه
  static void unlockOrientation() {
    // يمكن إضافة الكود هنا لإلغاء قفل الاتجاه
  }
}
