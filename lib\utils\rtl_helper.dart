import 'package:flutter/material.dart';

/// مساعد للتحكم في اتجاه النصوص والواجهة (RTL/LTR)
class RTLHelper {
  /// التحقق من كون النص عربي
  static bool isArabicText(String text) {
    if (text.isEmpty) return false;
    
    // فحص الأحرف العربية
    final arabicRegex = RegExp(r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]');
    return arabicRegex.hasMatch(text);
  }

  /// الحصول على اتجاه النص المناسب
  static TextDirection getTextDirection(String text) {
    return isArabicText(text) ? TextDirection.rtl : TextDirection.ltr;
  }

  /// widget لعرض النص بالاتجاه الصحيح
  static Widget buildDirectionalText(
    String text, {
    TextStyle? style,
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow? overflow,
    bool forceRTL = false,
  }) {
    final direction = forceRTL ? TextDirection.rtl : getTextDirection(text);
    
    return Directionality(
      textDirection: direction,
      child: Text(
        text,
        style: style,
        textAlign: textAlign ?? (direction == TextDirection.rtl ? TextAlign.right : TextAlign.left),
        maxLines: maxLines,
        overflow: overflow,
      ),
    );
  }

  /// widget للحاوي بالاتجاه الصحيح
  static Widget buildDirectionalContainer({
    required Widget child,
    bool forceRTL = true,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    Decoration? decoration,
  }) {
    return Directionality(
      textDirection: forceRTL ? TextDirection.rtl : TextDirection.ltr,
      child: Container(
        padding: padding,
        margin: margin,
        decoration: decoration,
        child: child,
      ),
    );
  }

  /// تحويل المسافات للاتجاه الصحيح
  static EdgeInsetsGeometry convertPaddingForRTL(EdgeInsetsGeometry padding) {
    if (padding is EdgeInsets) {
      return EdgeInsets.only(
        top: padding.top,
        bottom: padding.bottom,
        left: padding.right, // تبديل اليسار واليمين
        right: padding.left,
      );
    }
    return padding;
  }

  /// تحويل المحاذاة للاتجاه الصحيح
  static Alignment convertAlignmentForRTL(Alignment alignment) {
    return Alignment(
      -alignment.x, // عكس المحور الأفقي
      alignment.y,
    );
  }

  /// تحويل MainAxisAlignment للاتجاه الصحيح
  static MainAxisAlignment convertMainAxisAlignmentForRTL(MainAxisAlignment alignment) {
    switch (alignment) {
      case MainAxisAlignment.start:
        return MainAxisAlignment.end;
      case MainAxisAlignment.end:
        return MainAxisAlignment.start;
      default:
        return alignment;
    }
  }

  /// تحويل CrossAxisAlignment للاتجاه الصحيح
  static CrossAxisAlignment convertCrossAxisAlignmentForRTL(CrossAxisAlignment alignment) {
    switch (alignment) {
      case CrossAxisAlignment.start:
        return CrossAxisAlignment.end;
      case CrossAxisAlignment.end:
        return CrossAxisAlignment.start;
      default:
        return alignment;
    }
  }

  /// تحويل TextAlign للاتجاه الصحيح
  static TextAlign convertTextAlignForRTL(TextAlign? align) {
    if (align == null) return TextAlign.right; // افتراضي للعربية
    
    switch (align) {
      case TextAlign.left:
        return TextAlign.right;
      case TextAlign.right:
        return TextAlign.left;
      case TextAlign.start:
        return TextAlign.end;
      case TextAlign.end:
        return TextAlign.start;
      default:
        return align;
    }
  }

  /// إنشاء Row بالاتجاه الصحيح
  static Widget buildRTLRow({
    required List<Widget> children,
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
    MainAxisSize mainAxisSize = MainAxisSize.max,
  }) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Row(
        mainAxisAlignment: mainAxisAlignment,
        crossAxisAlignment: crossAxisAlignment,
        mainAxisSize: mainAxisSize,
        children: children,
      ),
    );
  }

  /// إنشاء ListTile بالاتجاه الصحيح
  static Widget buildRTLListTile({
    Widget? leading,
    Widget? title,
    Widget? subtitle,
    Widget? trailing,
    VoidCallback? onTap,
    EdgeInsetsGeometry? contentPadding,
  }) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: ListTile(
        leading: trailing, // تبديل المواضع
        title: title,
        subtitle: subtitle,
        trailing: leading,
        onTap: onTap,
        contentPadding: contentPadding,
      ),
    );
  }

  /// إنشاء AppBar بالاتجاه الصحيح
  static PreferredSizeWidget buildRTLAppBar({
    Widget? title,
    List<Widget>? actions,
    Widget? leading,
    bool automaticallyImplyLeading = true,
    Color? backgroundColor,
    double? elevation,
    bool centerTitle = true,
  }) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: AppBar(
        title: title,
        actions: leading != null ? [leading] : null, // تبديل المواضع
        leading: actions?.isNotEmpty == true ? actions!.first : null,
        automaticallyImplyLeading: automaticallyImplyLeading,
        backgroundColor: backgroundColor,
        elevation: elevation,
        centerTitle: centerTitle,
      ),
    );
  }

  /// إنشاء Drawer بالاتجاه الصحيح
  static Widget buildRTLDrawer({
    required Widget child,
    double? width,
  }) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: SizedBox(
        width: width,
        child: child,
      ),
    );
  }

  /// تحويل الأيقونات للاتجاه الصحيح
  static IconData convertIconForRTL(IconData icon) {
    // قائمة الأيقونات التي تحتاج تحويل
    const Map<IconData, IconData> iconMap = {
      Icons.arrow_forward: Icons.arrow_back,
      Icons.arrow_back: Icons.arrow_forward,
      Icons.arrow_forward_ios: Icons.arrow_back_ios,
      Icons.arrow_back_ios: Icons.arrow_forward_ios,
      Icons.chevron_left: Icons.chevron_right,
      Icons.chevron_right: Icons.chevron_left,
      Icons.keyboard_arrow_left: Icons.keyboard_arrow_right,
      Icons.keyboard_arrow_right: Icons.keyboard_arrow_left,
      Icons.first_page: Icons.last_page,
      Icons.last_page: Icons.first_page,
      Icons.navigate_before: Icons.navigate_next,
      Icons.navigate_next: Icons.navigate_before,
    };

    return iconMap[icon] ?? icon;
  }

  /// إنشاء أيقونة بالاتجاه الصحيح
  static Widget buildRTLIcon(
    IconData icon, {
    double? size,
    Color? color,
  }) {
    return Icon(
      convertIconForRTL(icon),
      size: size,
      color: color,
    );
  }

  /// إنشاء زر بالاتجاه الصحيح
  static Widget buildRTLButton({
    required Widget child,
    required VoidCallback? onPressed,
    IconData? icon,
    bool isIconButton = false,
  }) {
    if (isIconButton && icon != null) {
      return IconButton(
        onPressed: onPressed,
        icon: buildRTLIcon(icon),
      );
    }

    return Directionality(
      textDirection: TextDirection.rtl,
      child: ElevatedButton(
        onPressed: onPressed,
        child: child,
      ),
    );
  }

  /// تطبيق RTL على widget معين
  static Widget wrapWithRTL(Widget child) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: child,
    );
  }

  /// فحص ما إذا كان السياق الحالي RTL
  static bool isRTL(BuildContext context) {
    return Directionality.of(context) == TextDirection.rtl;
  }

  /// الحصول على اتجاه النص من السياق
  static TextDirection getDirectionFromContext(BuildContext context) {
    return Directionality.of(context);
  }

  /// تحويل Offset للاتجاه الصحيح
  static Offset convertOffsetForRTL(Offset offset, Size containerSize) {
    return Offset(
      containerSize.width - offset.dx, // عكس المحور الأفقي
      offset.dy,
    );
  }

  /// تحويل BorderRadius للاتجاه الصحيح
  static BorderRadius convertBorderRadiusForRTL(BorderRadius borderRadius) {
    return BorderRadius.only(
      topLeft: borderRadius.topRight,
      topRight: borderRadius.topLeft,
      bottomLeft: borderRadius.bottomRight,
      bottomRight: borderRadius.bottomLeft,
    );
  }

  /// إنشاء تخطيط متجاوب مع RTL
  static Widget buildResponsiveRTLLayout({
    required BuildContext context,
    required Widget child,
    bool forceRTL = true,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Directionality(
          textDirection: forceRTL ? TextDirection.rtl : TextDirection.ltr,
          child: child,
        );
      },
    );
  }
}
