import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/hadith_models.dart';
import '../utils/error_handler.dart';

class EnhancedHadithService {
  // Primary API source
  static const String _hadithApiUrl = 'https://api.hadith.gading.dev';
  // Additional API sources for future use
  // static const String _sunnhApiUrl = 'https://api.sunnah.com/v1';
  // static const String _hadithDbUrl = 'https://hadithapi.com/api';
  
  // Cache for hadith data
  static List<HadithCategory>? _cachedCategories;

  /// Get all hadith categories
  Future<List<HadithCategory>> getAllCategories() async {
    if (_cachedCategories != null) {
      return _cachedCategories!;
    }

    try {
      // Try primary API first
      final response = await http.get(
        Uri.parse('$_hadithApiUrl/books'),
        headers: {'Accept': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final books = data['data'] as List;
        
        List<HadithCategory> categories = [];
        
        for (int i = 0; i < books.length && i < 10; i++) {
          final book = books[i];
          final hadiths = await _getHadithsFromBook(book['id'] ?? '', limit: 5);
          
          categories.add(HadithCategory(
            id: i + 1,
            name: book['name'] ?? '',
            description: book['description'] ?? 'مجموعة من الأحاديث النبوية الشريفة',
            icon: _getIconForBook(book['name'] ?? ''),
            hadiths: hadiths,
          ));
        }
        
        _cachedCategories = categories;
        return categories;
      }
    } catch (e) {
      ErrorHandler.logError(e, null);
    }

    // Fallback to default categories
    _cachedCategories = HadithData.getDefaultCategories();
    return _cachedCategories!;
  }

  /// Get hadiths from specific book (internal method)
  Future<List<Hadith>> _getHadithsFromBook(String bookId, {int limit = 20}) async {
    try {
      final response = await http.get(
        Uri.parse('$_hadithApiUrl/books/$bookId/hadiths?page=1&limit=$limit'),
        headers: {'Accept': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final hadiths = data['data']['hadiths'] as List;
        
        return hadiths.map((hadith) => Hadith(
          id: hadith['id'] ?? 0,
          arabicText: hadith['arab'] ?? hadith['arabic'] ?? '',
          translation: hadith['id'] ?? hadith['translation'] ?? '',
          narrator: hadith['narrator'] ?? 'غير محدد',
          source: hadith['source'] ?? bookId,
          grade: hadith['grade'] ?? 'غير محدد',
          explanation: hadith['explanation'],
          keywords: _extractKeywords(hadith['arab'] ?? ''),
        )).toList();
      }
    } catch (e) {
      ErrorHandler.logError('Failed to fetch hadiths from book $bookId', error: e);
    }

    return [];
  }

  /// Get random hadith
  Future<Hadith?> getRandomHadith() async {
    try {
      final response = await http.get(
        Uri.parse('$_hadithApiUrl/hadiths/random'),
        headers: {'Accept': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final hadith = data['data'];
        
        return Hadith(
          id: hadith['id'] ?? 0,
          arabicText: hadith['arab'] ?? '',
          translation: hadith['id'] ?? '',
          narrator: hadith['narrator'] ?? 'غير محدد',
          source: hadith['source'] ?? 'غير محدد',
          grade: hadith['grade'] ?? 'غير محدد',
          explanation: hadith['explanation'],
          keywords: _extractKeywords(hadith['arab'] ?? ''),
        );
      }
    } catch (e) {
      ErrorHandler.logError('Failed to get random hadith', error: e);
    }

    // Fallback to a default hadith
    return _getDefaultHadith();
  }

  /// Search hadiths
  Future<List<Hadith>> searchHadiths(String query, {int limit = 20}) async {
    try {
      final response = await http.get(
        Uri.parse('$_hadithApiUrl/hadiths/search?q=${Uri.encodeComponent(query)}&limit=$limit'),
        headers: {'Accept': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final hadiths = data['data'] as List;
        
        return hadiths.map((hadith) => Hadith(
          id: hadith['id'] ?? 0,
          arabicText: hadith['arab'] ?? '',
          translation: hadith['id'] ?? '',
          narrator: hadith['narrator'] ?? 'غير محدد',
          source: hadith['source'] ?? 'غير محدد',
          grade: hadith['grade'] ?? 'غير محدد',
          explanation: hadith['explanation'],
          keywords: _extractKeywords(hadith['arab'] ?? ''),
        )).toList();
      }
    } catch (e) {
      ErrorHandler.logError('Failed to search hadiths', error: e);
    }

    return [];
  }

  /// Get hadith of the day
  Future<Hadith?> getHadithOfTheDay() async {
    try {
      // Use date as seed for consistent daily hadith
      final today = DateTime.now();
      final dateString = '${today.year}-${today.month}-${today.day}';
      
      final response = await http.get(
        Uri.parse('$_hadithApiUrl/hadiths/daily?date=$dateString'),
        headers: {'Accept': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final hadith = data['data'];
        
        return Hadith(
          id: hadith['id'] ?? 0,
          arabicText: hadith['arab'] ?? '',
          translation: hadith['id'] ?? '',
          narrator: hadith['narrator'] ?? 'غير محدد',
          source: hadith['source'] ?? 'غير محدد',
          grade: hadith['grade'] ?? 'غير محدد',
          explanation: hadith['explanation'],
          keywords: _extractKeywords(hadith['arab'] ?? ''),
        );
      }
    } catch (e) {
      ErrorHandler.logError('Failed to get hadith of the day', error: e);
    }

    // Fallback to random hadith
    return await getRandomHadith();
  }

  /// Get hadiths by topic/keyword
  Future<List<Hadith>> getHadithsByTopic(String topic, {int limit = 20}) async {
    // First try to search in cached categories
    final categories = await getAllCategories();
    List<Hadith> results = [];
    
    for (var category in categories) {
      for (var hadith in category.hadiths) {
        if (hadith.keywords.any((keyword) => keyword.contains(topic)) ||
            hadith.arabicText.contains(topic) ||
            hadith.translation.contains(topic)) {
          results.add(hadith);
          if (results.length >= limit) break;
        }
      }
      if (results.length >= limit) break;
    }
    
    // If not enough results, try API search
    if (results.length < limit) {
      final apiResults = await searchHadiths(topic, limit: limit - results.length);
      results.addAll(apiResults);
    }
    
    return results;
  }

  /// Get popular hadiths
  Future<List<Hadith>> getPopularHadiths({int limit = 10}) async {
    try {
      final response = await http.get(
        Uri.parse('$_hadithApiUrl/hadiths/popular?limit=$limit'),
        headers: {'Accept': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final hadiths = data['data'] as List;
        
        return hadiths.map((hadith) => Hadith(
          id: hadith['id'] ?? 0,
          arabicText: hadith['arab'] ?? '',
          translation: hadith['id'] ?? '',
          narrator: hadith['narrator'] ?? 'غير محدد',
          source: hadith['source'] ?? 'غير محدد',
          grade: hadith['grade'] ?? 'غير محدد',
          explanation: hadith['explanation'],
          keywords: _extractKeywords(hadith['arab'] ?? ''),
        )).toList();
      }
    } catch (e) {
      ErrorHandler.logError('Failed to get popular hadiths', error: e);
    }

    // Fallback to some well-known hadiths from default data
    return _getPopularHadithsFallback();
  }

  /// Get hadith by category
  Future<List<Hadith>> getHadithsByCategory(String categoryName) async {
    final categories = await getAllCategories();
    
    for (var category in categories) {
      if (category.name.contains(categoryName)) {
        return category.hadiths;
      }
    }
    
    return [];
  }

  /// Get available topics/keywords
  Future<List<String>> getAvailableTopics() async {
    final categories = await getAllCategories();
    Set<String> topics = {};
    
    for (var category in categories) {
      for (var hadith in category.hadiths) {
        topics.addAll(hadith.keywords);
      }
    }
    
    return topics.toList()..sort();
  }

  /// Clear cache
  void clearCache() {
    _cachedCategories = null;
  }

  /// Extract keywords from Arabic text (simple implementation)
  List<String> _extractKeywords(String arabicText) {
    // Simple keyword extraction - in a real app, you'd use more sophisticated NLP
    final commonWords = ['الله', 'رسول', 'صلى', 'عليه', 'وسلم', 'قال', 'عن', 'أن', 'من', 'في', 'إلى', 'على', 'هذا', 'ذلك'];
    final words = arabicText.split(' ');
    final keywords = <String>[];
    
    for (var word in words) {
      final cleanWord = word.replaceAll(RegExp(r'[^\u0600-\u06FF\u0750-\u077F]'), '');
      if (cleanWord.length > 2 && !commonWords.contains(cleanWord)) {
        keywords.add(cleanWord);
      }
    }
    
    return keywords.take(5).toList(); // Return top 5 keywords
  }

  /// Get icon for book name
  String _getIconForBook(String bookName) {
    if (bookName.contains('البخاري')) return 'bukhari';
    if (bookName.contains('مسلم')) return 'muslim';
    if (bookName.contains('الترمذي')) return 'tirmidhi';
    if (bookName.contains('أبي داود')) return 'abudawud';
    if (bookName.contains('النسائي')) return 'nasai';
    if (bookName.contains('ابن ماجه')) return 'ibnmajah';
    return 'hadith';
  }

  /// Get default hadith
  Hadith _getDefaultHadith() {
    return Hadith(
      id: 1,
      arabicText: 'أَعْلَمُ بِالْيَوْمِ وَمَا فِي الْآخِرَةِ',
      translation: 'أعلم باليوم وما في الآخرة',
      narrator: 'عمر بن الخطاب رضي الله عنه',
      source: 'صحيح البخاري',
      grade: 'صحيح',
      explanation: 'هذا الحديث يؤكد على أهمية التعلم والتعلم المستمر',
      keywords: ['العلم', 'التعلم', 'الحياة'],
    );
  }

  /// Get popular hadiths fallback
  List<Hadith> _getPopularHadithsFallback() {
    return [
      Hadith(
        id: 1,
        arabicText: 'أَعْلَمُ بِالْيَوْمِ وَمَا فِي الْآخِرَةِ',
        translation: 'أعلم باليوم وما في الآخرة',
        narrator: 'عمر بن الخطاب رضي الله عنه',
        source: 'صحيح البخاري',
        grade: 'صحيح',
        keywords: ['العلم', 'التعلم', 'الحياة'],
      ),
      Hadith(
        id: 2,
        arabicText: 'أَلْيَوْمَ أَحَبُّ إِلَيَّ مِنَ الْآخِرَةِ',
        translation: 'اليوم أحب إليّ من الآخرة',
        narrator: 'عمر بن الخطاب رضي الله عنه',
        source: 'صحيح البخاري',
        grade: 'صحيح',
        keywords: ['اليوم', 'الآخرة', 'الحب'],
      ),
    ];
  }

  /// Get hadith statistics
  Future<Map<String, dynamic>> getHadithStatistics() async {
    final categories = await getAllCategories();
    int totalHadiths = 0;
    
    for (var category in categories) {
      totalHadiths += category.hadiths.length;
    }
    
    return {
      'totalCategories': categories.length,
      'totalHadiths': totalHadiths,
      'averagePerCategory': categories.isNotEmpty ? totalHadiths / categories.length : 0,
      'lastUpdated': DateTime.now().toIso8601String(),
    };
  }

  /// Get enhanced categories with API data
  Future<List<HadithCategory>> getEnhancedCategories() async {
    final defaultCategories = HadithData.getDefaultCategories();
    
    // Try to enhance with API data
    try {
      final apiCategories = await getAllCategories();
      if (apiCategories.isNotEmpty) {
        // Merge default and API categories
        final mergedCategories = <HadithCategory>[];
        mergedCategories.addAll(defaultCategories);
        
        // Add unique API categories
        for (var apiCategory in apiCategories) {
          if (!mergedCategories.any((cat) => cat.name == apiCategory.name)) {
            mergedCategories.add(apiCategory);
          }
        }
        
        return mergedCategories;
      }
    } catch (e) {
      ErrorHandler.logError('Failed to get enhanced categories', error: e);
    }
    
    return defaultCategories;
  }

  /// Check if data is cached
  bool get isCached => _cachedCategories != null;

  /// Get cache timestamp
  DateTime? get cacheTimestamp => _cachedCategories != null ? DateTime.now() : null;
}
