import 'package:flutter/material.dart';

/// نظام الثيمات المتقدم للتطبيق
class AppThemes {
  // ألوان الثيمات المختلفة
  static const Map<String, Map<String, Color>> themeColors = {
    'green': {
      'primary': Color(0xFF2E7D32),
      'primaryLight': Color(0xFF4CAF50),
      'primaryDark': Color(0xFF1B5E20),
      'secondary': Color(0xFF81C784),
      'accent': Color(0xFFFFD700),
      'background': Color(0xFFF1F8E9),
      'surface': Color(0xFFFFFFFF),
      'cardBackground': Color(0xFFF8F9FA),
    },
    'blue': {
      'primary': Color(0xFF1565C0),
      'primaryLight': Color(0xFF2196F3),
      'primaryDark': Color(0xFF0D47A1),
      'secondary': Color(0xFF64B5F6),
      'accent': Color(0xFFFFD700),
      'background': Color(0xFFE3F2FD),
      'surface': Color(0xFFFFFFFF),
      'cardBackground': Color(0xFFF8F9FA),
    },
    'golden': {
      'primary': Color(0xFFB8860B),
      'primaryLight': Color(0xFFDAA520),
      'primaryDark': Color(0xFF8B6914),
      'secondary': Color(0xFFDEB887),
      'accent': Color(0xFFFFD700),
      'background': Color(0xFFFFFDF5),
      'surface': Color(0xFFFFFFFF),
      'cardBackground': Color(0xFFFFFAF0),
    },
    'dark': {
      'primary': Color(0xFF4CAF50),
      'primaryLight': Color(0xFF81C784),
      'primaryDark': Color(0xFF2E7D32),
      'secondary': Color(0xFF81C784),
      'accent': Color(0xFFFFD700),
      'background': Color(0xFF121212),
      'surface': Color(0xFF1E1E1E),
      'cardBackground': Color(0xFF2D2D2D),
    },
  };

  // خلفيات إسلامية
  static const Map<String, String> islamicBackgrounds = {
    'mosque': 'assets/images/backgrounds/mosque_pattern.png',
    'geometric': 'assets/images/backgrounds/islamic_geometric.png',
    'calligraphy': 'assets/images/backgrounds/arabic_calligraphy.png',
    'stars': 'assets/images/backgrounds/islamic_stars.png',
    'simple': 'assets/images/backgrounds/simple_pattern.png',
  };

  // أحجام الخطوط
  static const Map<String, double> fontSizes = {
    'small': 14.0,
    'medium': 18.0,
    'large': 22.0,
    'extraLarge': 26.0,
  };

  // إنشاء ثيم فاتح
  static ThemeData createLightTheme(String colorScheme, double fontSize) {
    final colors = themeColors[colorScheme] ?? themeColors['green']!;
    
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      
      // نظام الألوان
      colorScheme: ColorScheme.light(
        primary: colors['primary']!,
        primaryContainer: colors['primaryLight']!,
        secondary: colors['secondary']!,
        secondaryContainer: colors['secondary']!.withValues(alpha: 0.3),
        surface: colors['surface']!,
        surfaceContainerHighest: colors['cardBackground']!,
        background: colors['background']!,
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        onSurface: const Color(0xFF1C1B1F),
        onBackground: const Color(0xFF1C1B1F),
      ),

      // شريط التطبيق
      appBarTheme: AppBarTheme(
        backgroundColor: colors['primary'],
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          color: Colors.white,
          fontSize: fontSize + 2,
          fontWeight: FontWeight.bold,
          fontFamily: 'Tajawal',
        ),
        iconTheme: const IconThemeData(color: Colors.white),
      ),

      // النصوص
      textTheme: _createTextTheme(fontSize, false),

      // البطاقات
      cardTheme: CardTheme(
        color: colors['cardBackground'],
        elevation: 4,
        shadowColor: colors['primary']!.withValues(alpha: 0.2),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),

      // الأزرار
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: colors['primary'],
          foregroundColor: Colors.white,
          elevation: 4,
          shadowColor: colors['primary']!.withValues(alpha: 0.3),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),

      // أزرار النص
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: colors['primary'],
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),

      // أزرار الأيقونات
      iconButtonTheme: IconButtonThemeData(
        style: IconButton.styleFrom(
          foregroundColor: colors['primary'],
        ),
      ),

      // شريط التنقل السفلي
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: colors['surface'],
        selectedItemColor: colors['primary'],
        unselectedItemColor: Colors.grey[600],
        type: BottomNavigationBarType.fixed,
        elevation: 8,
      ),

      // مؤشر التقدم
      progressIndicatorTheme: ProgressIndicatorThemeData(
        color: colors['primary'],
      ),

      // الأيقونات
      iconTheme: IconThemeData(
        color: colors['primary'],
        size: 24,
      ),

      // الخطوط الفاصلة
      dividerTheme: DividerThemeData(
        color: Colors.grey[300],
        thickness: 1,
      ),

      // حقول الإدخال
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: colors['primary']!),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: colors['primary']!, width: 2),
        ),
        labelStyle: TextStyle(color: colors['primary']),
        prefixIconColor: colors['primary'],
      ),

      // الحوارات
      dialogTheme: DialogTheme(
        backgroundColor: colors['surface'],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        elevation: 8,
      ),

      // شريط التمرير
      scrollbarTheme: ScrollbarThemeData(
        thumbColor: WidgetStateProperty.all(colors['primary']!.withValues(alpha: 0.5)),
        trackColor: WidgetStateProperty.all(colors['primary']!.withValues(alpha: 0.1)),
      ),
    );
  }

  // إنشاء ثيم داكن
  static ThemeData createDarkTheme(double fontSize) {
    final colors = themeColors['dark']!;
    
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      
      // نظام الألوان
      colorScheme: ColorScheme.dark(
        primary: colors['primary']!,
        primaryContainer: colors['primaryDark']!,
        secondary: colors['secondary']!,
        secondaryContainer: colors['secondary']!.withValues(alpha: 0.3),
        surface: colors['surface']!,
        surfaceContainerHighest: colors['cardBackground']!,
        background: colors['background']!,
        onPrimary: Colors.black,
        onSecondary: Colors.black,
        onSurface: Colors.white,
        onBackground: Colors.white,
      ),

      // شريط التطبيق
      appBarTheme: AppBarTheme(
        backgroundColor: colors['surface'],
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          color: Colors.white,
          fontSize: fontSize + 2,
          fontWeight: FontWeight.bold,
          fontFamily: 'Tajawal',
        ),
        iconTheme: const IconThemeData(color: Colors.white),
      ),

      // النصوص
      textTheme: _createTextTheme(fontSize, true),

      // البطاقات
      cardTheme: CardTheme(
        color: colors['cardBackground'],
        elevation: 4,
        shadowColor: Colors.black.withValues(alpha: 0.3),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),

      // الأزرار
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: colors['primary'],
          foregroundColor: Colors.black,
          elevation: 4,
          shadowColor: Colors.black.withValues(alpha: 0.5),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),

      // باقي الإعدادات مشابهة للثيم الفاتح مع تعديل الألوان
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: colors['surface'],
        selectedItemColor: colors['primary'],
        unselectedItemColor: Colors.grey[400],
        type: BottomNavigationBarType.fixed,
        elevation: 8,
      ),
    );
  }

  // إنشاء نظام النصوص
  static TextTheme _createTextTheme(double baseFontSize, bool isDark) {
    final textColor = isDark ? Colors.white : const Color(0xFF1C1B1F);
    final secondaryTextColor = isDark ? Colors.white70 : Colors.grey[600];

    return TextTheme(
      // العناوين الكبيرة
      headlineLarge: TextStyle(
        fontSize: baseFontSize + 8,
        fontWeight: FontWeight.bold,
        color: textColor,
        fontFamily: 'Tajawal',
      ),
      headlineMedium: TextStyle(
        fontSize: baseFontSize + 4,
        fontWeight: FontWeight.bold,
        color: textColor,
        fontFamily: 'Tajawal',
      ),
      headlineSmall: TextStyle(
        fontSize: baseFontSize + 2,
        fontWeight: FontWeight.w600,
        color: textColor,
        fontFamily: 'Tajawal',
      ),

      // عناوين البطاقات
      titleLarge: TextStyle(
        fontSize: baseFontSize + 2,
        fontWeight: FontWeight.bold,
        color: textColor,
        fontFamily: 'Tajawal',
      ),
      titleMedium: TextStyle(
        fontSize: baseFontSize,
        fontWeight: FontWeight.w600,
        color: textColor,
        fontFamily: 'Tajawal',
      ),
      titleSmall: TextStyle(
        fontSize: baseFontSize - 2,
        fontWeight: FontWeight.w500,
        color: textColor,
        fontFamily: 'Tajawal',
      ),

      // النصوص العادية
      bodyLarge: TextStyle(
        fontSize: baseFontSize,
        fontWeight: FontWeight.normal,
        color: textColor,
        fontFamily: 'Tajawal',
        height: 1.6,
      ),
      bodyMedium: TextStyle(
        fontSize: baseFontSize - 2,
        fontWeight: FontWeight.normal,
        color: textColor,
        fontFamily: 'Tajawal',
        height: 1.5,
      ),
      bodySmall: TextStyle(
        fontSize: baseFontSize - 4,
        fontWeight: FontWeight.normal,
        color: secondaryTextColor,
        fontFamily: 'Tajawal',
        height: 1.4,
      ),

      // التسميات
      labelLarge: TextStyle(
        fontSize: baseFontSize - 2,
        fontWeight: FontWeight.w500,
        color: textColor,
        fontFamily: 'Tajawal',
      ),
      labelMedium: TextStyle(
        fontSize: baseFontSize - 4,
        fontWeight: FontWeight.w500,
        color: secondaryTextColor,
        fontFamily: 'Tajawal',
      ),
      labelSmall: TextStyle(
        fontSize: baseFontSize - 6,
        fontWeight: FontWeight.w400,
        color: secondaryTextColor,
        fontFamily: 'Tajawal',
      ),
    );
  }

  // الحصول على لون الثيم
  static Color getThemeColor(String themeName, String colorKey) {
    return themeColors[themeName]?[colorKey] ?? themeColors['green']![colorKey]!;
  }

  // قائمة الثيمات المتاحة
  static List<Map<String, dynamic>> getAvailableThemes() {
    return [
      {
        'key': 'green',
        'name': 'الأخضر الإسلامي',
        'description': 'اللون التقليدي الهادئ',
        'primaryColor': themeColors['green']!['primary']!,
        'icon': Icons.eco,
      },
      {
        'key': 'blue',
        'name': 'الأزرق الهادئ',
        'description': 'لون السماء والبحر',
        'primaryColor': themeColors['blue']!['primary']!,
        'icon': Icons.water_drop,
      },
      {
        'key': 'golden',
        'name': 'الذهبي الفاخر',
        'description': 'لون الفخامة والأناقة',
        'primaryColor': themeColors['golden']!['primary']!,
        'icon': Icons.star,
      },
      {
        'key': 'dark',
        'name': 'الوضع الليلي',
        'description': 'مريح للعينين في الظلام',
        'primaryColor': themeColors['dark']!['primary']!,
        'icon': Icons.dark_mode,
      },
    ];
  }
}
