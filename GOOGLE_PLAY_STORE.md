# دليل نشر تطبيق قرآني على Google Play Store

## 📋 قائمة المتطلبات

### ✅ المتطلبات التقنية المكتملة:
- [x] **Android API Level 21+** (Android 5.0+)
- [x] **Target SDK 34** (Android 14)
- [x] **App Bundle Format** (.aab)
- [x] **ProGuard/R8 Optimization** 
- [x] **64-bit Architecture Support** (arm64-v8a, armeabi-v7a, x86_64)
- [x] **Network Security Config**
- [x] **Data Extraction Rules**
- [x] **Locale Configuration**

### ✅ الأذونات المطلوبة:
- [x] `INTERNET` - للوصول للـ APIs
- [x] `ACCESS_NETWORK_STATE` - فحص حالة الشبكة
- [x] `WAKE_LOCK` - تشغيل الصوتيات
- [x] `POST_NOTIFICATIONS` - الإشعارات (Android 13+)
- [x] `ACCESS_COARSE_LOCATION` - أوقات الصلاة (اختياري)
- [x] `ACCESS_FINE_LOCATION` - أوقات الصلاة الدقيقة (اختياري)
- [x] `VIBRATE` - اهتزاز الإشعارات
- [x] `RECEIVE_BOOT_COMPLETED` - إعادة جدولة الإشعارات

### ✅ سياسات Google Play:
- [x] **Privacy Policy** - متوفرة في PRIVACY_POLICY.md
- [x] **Content Rating** - مناسب لجميع الأعمار
- [x] **Target Audience** - المسلمون من جميع الأعمار
- [x] **Data Safety** - لا يتم جمع بيانات شخصية
- [x] **Permissions Justification** - جميع الأذونات مبررة

## 📱 معلومات التطبيق للمتجر

### 🏷️ البيانات الأساسية:
- **اسم التطبيق**: قرآني - القرآن والأذكار
- **اسم الحزمة**: com.islamicapp.quraan
- **الفئة**: Books & Reference / Lifestyle
- **التصنيف**: Everyone (مناسب للجميع)
- **اللغات المدعومة**: العربية (أساسي) + 9 لغات إضافية

### 📝 الوصف المختصر (80 حرف):
"تطبيق إسلامي شامل للقرآن والأذكار والأحاديث مع أوقات الصلاة"

### 📄 الوصف الكامل:
```
🕌 تطبيق قرآني - التطبيق الإسلامي الشامل

📖 القرآن الكريم:
• 114 سورة كاملة مع التفسير
• أكثر من 100 قارئ مشهور
• تحميل للاستماع بدون إنترنت
• البحث في الآيات والسور
• العلامات المرجعية وتتبع التقدم

🤲 الأذكار والأدعية:
• أذكار الصباح والمساء
• أذكار النوم والاستيقاظ
• أذكار الصلاة والطعام
• عدادات تفاعلية ذكية

📿 السبحة الإلكترونية:
• سبحة رقمية تفاعلية
• عدادات متعددة قابلة للحفظ
• تأثيرات بصرية جميلة

📚 الأحاديث النبوية:
• أحاديث صحيحة من الكتب الستة
• حديث اليوم
• تصنيف حسب الموضوع
• نظام المفضلة

🕐 أوقات الصلاة:
• أوقات دقيقة حسب موقعك
• 14 طريقة حساب مختلفة
• التاريخ الهجري
• اتجاه القبلة

✨ مميزات إضافية:
• يعمل بدون إنترنت
• الوضع الليلي والنهاري
• خطوط عربية جميلة
• إشعارات تذكيرية
• مجاني تماماً بدون إعلانات

🔒 الخصوصية:
• لا يتم جمع أي بيانات شخصية
• جميع البيانات محفوظة محلياً
• لا توجد تتبع أو إعلانات

جعله الله في ميزان حسناتنا وحسناتكم 🤲
```

### 🏷️ الكلمات المفتاحية:
قرآن، إسلام، أذكار، حديث، صلاة، مسلم، تلاوة، تفسير، سبحة، أوقات الصلاة

## 🎨 الأصول المطلوبة

### 📱 أيقونة التطبيق:
- **512x512 px** - أيقونة عالية الدقة للمتجر
- **192x192 px** - أيقونة متوسطة
- **96x96 px** - أيقونة صغيرة
- **48x48 px** - أيقونة مصغرة

### 📸 لقطات الشاشة:
1. **الشاشة الرئيسية** - عرض الميزات الأساسية
2. **شاشة القرآن** - عرض السور والآيات
3. **شاشة الأذكار** - عرض الأذكار والعدادات
4. **شاشة السبحة** - السبحة الإلكترونية
5. **شاشة الأحاديث** - عرض الأحاديث
6. **شاشة أوقات الصلاة** - عرض الأوقات
7. **شاشة الإعدادات** - الوضع الليلي والخيارات

### 🎬 فيديو ترويجي (اختياري):
- مدة: 30-60 ثانية
- عرض الميزات الأساسية
- بدون صوت أو بموسيقى هادئة

## 🔧 خطوات النشر

### 1. إعداد التوقيع:
```bash
# إنشاء مفتاح التوقيع
keytool -genkey -v -keystore quraan-release-key.keystore -alias quraan -keyalg RSA -keysize 2048 -validity 10000

# إعداد key.properties
echo "storePassword=YOUR_STORE_PASSWORD" > android/key.properties
echo "keyPassword=YOUR_KEY_PASSWORD" >> android/key.properties
echo "keyAlias=quraan" >> android/key.properties
echo "storeFile=../quraan-release-key.keystore" >> android/key.properties
```

### 2. بناء App Bundle:
```bash
# تنظيف المشروع
flutter clean
flutter pub get

# بناء للإنتاج
flutter build appbundle --release

# الملف الناتج في:
# build/app/outputs/bundle/release/app-release.aab
```

### 3. اختبار التطبيق:
```bash
# اختبار على أجهزة مختلفة
flutter run --release

# فحص الأداء
flutter run --profile

# تحليل الكود
flutter analyze
```

### 4. رفع على Google Play Console:
1. إنشاء حساب مطور (25$ رسوم لمرة واحدة)
2. إنشاء تطبيق جديد
3. رفع App Bundle
4. ملء معلومات التطبيق
5. إعداد Content Rating
6. إضافة Privacy Policy
7. إرسال للمراجعة

## 📊 إعدادات المتجر

### 🎯 الجمهور المستهدف:
- **العمر**: جميع الأعمار (Everyone)
- **الجغرافيا**: عالمي مع تركيز على الدول الإسلامية
- **اللغة**: العربية أساسي + إنجليزية

### 💰 نموذج الأعمال:
- **مجاني تماماً**
- **بدون إعلانات**
- **بدون مشتريات داخلية**
- **مفتوح المصدر**

### 🔒 Data Safety:
- **لا يتم جمع بيانات**: ✅
- **لا يتم مشاركة بيانات**: ✅
- **التشفير أثناء النقل**: ✅
- **إمكانية حذف البيانات**: ✅

## ⚠️ نصائح مهمة

### ✅ للموافقة السريعة:
- استخدم وصف واضح ومفصل
- أضف لقطات شاشة عالية الجودة
- تأكد من عمل جميع الميزات
- اختبر على أجهزة مختلفة
- اتبع إرشادات Google Play

### ❌ تجنب:
- المحتوى المضلل
- الأذونات غير المبررة
- الأخطاء البرمجية
- انتهاك حقوق الطبع
- المحتوى غير المناسب

## 📞 الدعم

للمساعدة في النشر:
- **البريد الإلكتروني**: <EMAIL>
- **GitHub Issues**: [رفع مشكلة](https://github.com/islamicapp/quraan/issues)

---

**بالتوفيق في نشر التطبيق! جعله الله في ميزان حسناتكم** 🤲
