import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/audio_provider.dart';
import '../providers/settings_provider.dart';

class AudioPlayerBar extends StatefulWidget {
  const AudioPlayerBar({super.key});

  @override
  State<AudioPlayerBar> createState() => _AudioPlayerBarState();
}

class _AudioPlayerBarState extends State<AudioPlayerBar>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _pulseController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _slideController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<AudioProvider, SettingsProvider>(
      builder: (context, audioProvider, settings, child) {
        if (audioProvider.currentSurah == null) {
          _slideController.reverse();
          return const SizedBox.shrink();
        }

        _slideController.forward();

        return SlideTransition(
          position: _slideAnimation,
          child: Container(
            height: 120,
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).colorScheme.primary,
                  Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
                ],
                begin: Alignment.topRight,
                end: Alignment.bottomLeft,
              ),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Column(
                children: [
                  // الصف الأول: معلومات السورة وأزرار التحكم
                  Row(
                    children: [
                      // معلومات السورة
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              audioProvider.currentSurah!.name,
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: settings.fontSize,
                                fontWeight: FontWeight.bold,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 2),
                            Text(
                              audioProvider.currentReciter?.arabicName ?? 'قارئ غير محدد',
                              style: TextStyle(
                                color: Colors.white70,
                                fontSize: settings.fontSize - 2,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),

                      // أزرار التحكم
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                      // السابق
                      _buildControlButton(
                        icon: Icons.skip_previous_rounded,
                        onPressed: audioProvider.canGoPrevious
                            ? () => audioProvider.playPreviousSurah()
                            : null,
                      ),

                      const SizedBox(width: 4),

                      // ترجيع 30 ثانية
                      _buildControlButton(
                        icon: Icons.replay_30_rounded,
                        onPressed: () => audioProvider.seekBackward(),
                      ),

                      const SizedBox(width: 8),

                      // تشغيل/إيقاف
                      AnimatedBuilder(
                        animation: _pulseAnimation,
                        builder: (context, child) {
                          return Transform.scale(
                            scale: audioProvider.isPlaying && settings.enableAnimations
                                ? _pulseAnimation.value
                                : 1.0,
                            child: _buildMainPlayButton(audioProvider),
                          );
                        },
                      ),

                      const SizedBox(width: 8),

                      // تقديم 30 ثانية
                      _buildControlButton(
                        icon: Icons.forward_30_rounded,
                        onPressed: () => audioProvider.seekForward(),
                      ),

                      const SizedBox(width: 4),

                      // التالي
                      _buildControlButton(
                        icon: Icons.skip_next_rounded,
                        onPressed: audioProvider.canGoNext
                            ? () => audioProvider.playNextSurah()
                            : null,
                      ),
                    ],
                  ),

                  // الصف الثاني: شريط التقدم
                  const SizedBox(height: 8),
                  GestureDetector(
                    onTapDown: (details) {
                      final RenderBox box = context.findRenderObject() as RenderBox;
                      final localPosition = box.globalToLocal(details.globalPosition);
                      final progressBarWidth = MediaQuery.of(context).size.width - 64; // minus margins and padding
                      final tapPosition = localPosition.dx - 32; // minus left margin and padding
                      final progress = (tapPosition / progressBarWidth).clamp(0.0, 1.0);
                      final newPosition = Duration(
                        milliseconds: (audioProvider.totalDuration.inMilliseconds * progress).round(),
                      );
                      audioProvider.seekTo(newPosition);
                    },
                    child: SizedBox(
                      height: 20, // زيادة المساحة القابلة للنقر
                      child: Center(
                        child: Container(
                          height: 4,
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.3),
                            borderRadius: BorderRadius.circular(2),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(2),
                            child: LinearProgressIndicator(
                              value: audioProvider.progress,
                              backgroundColor: Colors.transparent,
                              valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback? onPressed,
  }) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(20),
      ),
      child: IconButton(
        icon: Icon(
          icon,
          color: Colors.white,
          size: 20,
        ),
        onPressed: onPressed,
        padding: EdgeInsets.zero,
      ),
    );
  }

  Widget _buildMainPlayButton(AudioProvider audioProvider) {
    return Container(
      width: 56,
      height: 56,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(28),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: IconButton(
        icon: Icon(
          audioProvider.isPlaying
              ? Icons.pause_rounded
              : Icons.play_arrow_rounded,
          color: Theme.of(context).colorScheme.primary,
          size: 28,
        ),
        onPressed: () {
          if (audioProvider.isPlaying) {
            audioProvider.pause();
          } else {
            audioProvider.resume();
          }
        },
        padding: EdgeInsets.zero,
      ),
    );
  }
}
