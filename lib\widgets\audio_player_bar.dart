import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/audio_provider.dart';

class AudioPlayerBar extends StatelessWidget {
  const AudioPlayerBar({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AudioProvider>(
      builder: (context, audioProvider, child) {
        if (audioProvider.currentSurah == null) {
          return const SizedBox.shrink();
        }

        return Container(
          height: 80,
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                // معلومات السورة
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        audioProvider.currentSurah!.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        audioProvider.currentReciter?.arabicName ?? 'قارئ غير محدد',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                
                // أزرار التحكم
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // السورة السابقة
                    IconButton(
                      icon: const Icon(Icons.skip_previous_rounded),
                      onPressed: audioProvider.canGoPrevious
                        ? () => audioProvider.playPreviousSurah()
                        : null,
                      color: audioProvider.canGoPrevious
                        ? const Color(0xFF4CAF50)
                        : Colors.grey,
                    ),
                    
                    // تشغيل/إيقاف
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFF4CAF50), Color(0xFF66BB6A)],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(24),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0xFF4CAF50).withValues(alpha: 0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: IconButton(
                        icon: Icon(
                          audioProvider.isPlaying 
                            ? Icons.pause_rounded 
                            : Icons.play_arrow_rounded,
                          color: Colors.white,
                          size: 24,
                        ),
                        onPressed: () {
                          if (audioProvider.isPlaying) {
                            audioProvider.pause();
                          } else {
                            audioProvider.resume();
                          }
                        },
                      ),
                    ),
                    
                    // السورة التالية
                    IconButton(
                      icon: const Icon(Icons.skip_next_rounded),
                      onPressed: audioProvider.canGoNext
                        ? () => audioProvider.playNextSurah()
                        : null,
                      color: audioProvider.canGoNext
                        ? const Color(0xFF4CAF50)
                        : Colors.grey,
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
