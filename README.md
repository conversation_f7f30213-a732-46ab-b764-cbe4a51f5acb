# quraan

A new Flutter project.

## Getting Started

# تطبيق قرآني 📱

تطبيق إسلامي شامل مطور بـ Flutter يحتوي على القرآن الكريم والأذكار والأحاديث النبوية الشريفة.

## الميزات الرئيسية ✨

### 📖 القرآن الكريم
- عرض سور وآيات القرآن الكريم كاملاً
- تصفح حسب السورة أو الجزء أو الصفحة
- الاستماع للقرآن بأصوات قراء مختلفين
- تحميل التلاوات للاستماع بدون إنترنت
- عرض التفسير لكل آية (السعدي، ابن كثير، الطبري)
- إضافة علامات مرجعية للآيات المفضلة
- تتبع تقدم القراءة

### 🤲 الأذكار
- أذكار الصباح والمساء
- أذكار النوم وأذكار الصلاة
- أذكار متنوعة للمناسبات المختلفة
- عداد تنازلي لكل ذكر
- تتبع التقدم اليومي
- تصميم جذاب ومريح للعين

### 📿 السبحة الإلكترونية
- سبحة رقمية تفاعلية
- اختيار الذكر المفضل
- تتبع عدد التسبيحات
- إعادة التصفير وحفظ العدادات
- تأثيرات بصرية وحسية

### 📚 الأحاديث الصحيحة
- مكتبة أحاديث نبوية صحيحة
- تصنيف حسب الموضوع (أخلاق، صلاة، صيام، إلخ)
- حديث اليوم
- إضافة الأحاديث للمفضلة
- البحث في الأحاديث

### ⚙️ إعدادات متقدمة
- الوضع الليلي/النهاري
- تخصيص حجم الخط
- إشعارات تذكيرية
- دعم اللغة العربية كاملاً
- قاعدة بيانات محلية

## التقنيات المستخدمة 🛠️

### إطار العمل والتقنيات الأساسية
- **Flutter & Dart** - إطار العمل الأساسي
- **Provider** - إدارة الحالة
- **Hive** - قاعدة بيانات محلية سريعة
- **SQLite** - قاعدة بيانات للاستعلامات المعقدة
- **AudioPlayers** - تشغيل الصوتيات
- **Local Notifications** - الإشعارات المحلية
- **Google Fonts** - خطوط عربية جميلة

### APIs المجانية المستخدمة 🔗
- **[Quran.com API](https://api.quran.com)** - القرآن الكريم والتفاسير
- **[MP3Quran.net API](https://mp3quran.net/api)** - أكثر من 100 قارئ
- **[QuranEnc.com API](https://quranenc.com/api)** - التفسير الميسر
- **[Aladhan.com API](https://aladhan.com/prayer-times-api)** - أوقات الصلاة
- **[Hadith API](https://api.hadith.gading.dev)** - الأحاديث النبوية
- **[Hisn Elmuslim API](https://github.com/mohamednagy/Hisn-Elmuslim-API)** - الأذكار

## كيفية التشغيل 🚀

### المتطلبات
- Flutter SDK (3.7.2 أو أحدث)
- Dart SDK
- Android Studio أو VS Code
- جهاز Android أو iOS أو محاكي

### خطوات التشغيل

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-username/quraan-app.git
cd quraan-app
```

2. **تثبيت التبعيات**
```bash
flutter pub get
```

3. **توليد ملفات Hive**
```bash
flutter packages pub run build_runner build
```

4. **تشغيل التطبيق**
```bash
flutter run
```

## بنية المشروع 📁

```
lib/
├── main.dart                 # نقطة البداية
├── models/                   # نماذج البيانات
│   ├── quran_models.dart
│   ├── azkar_models.dart
│   └── hadith_models.dart
├── providers/                # مزودي الحالة
│   ├── app_provider.dart
│   ├── quran_provider.dart
│   ├── azkar_provider.dart
│   ├── hadith_provider.dart
│   └── settings_provider.dart
├── services/                 # الخدمات
│   ├── database_service.dart
│   ├── audio_service.dart
│   └── notification_service.dart
├── screens/                  # الشاشات
│   ├── home_screen.dart
│   ├── quran/
│   ├── azkar/
│   ├── tasbih/
│   ├── hadith/
│   └── settings/
└── widgets/                  # العناصر المخصصة

assets/
├── data/                     # بيانات التطبيق
├── images/                   # الصور
├── audio/                    # الملفات الصوتية
└── fonts/                    # الخطوط
```

## المساهمة 🤝

نرحب بمساهماتكم! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إضافة التغييرات
4. إرسال Pull Request

## الترخيص 📄

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم والتواصل 📞

- **البريد الإلكتروني**: <EMAIL>
- **GitHub Issues**: [رفع مشكلة](https://github.com/your-username/quraan-app/issues)

## شكر خاص 🙏

- **القرآن الكريم**: من مصحف المدينة المنورة
- **الأحاديث**: من صحيح البخاري ومسلم
- **الأذكار**: من حصن المسلم
- **التلاوات**: من موقع mp3quran.net

---

**جعله الله في ميزان حسناتنا وحسناتكم** 🤲
