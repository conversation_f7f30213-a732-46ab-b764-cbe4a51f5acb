import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';

/// Security utilities for the app
class SecurityUtils {
  static final SecurityUtils _instance = SecurityUtils._internal();
  factory SecurityUtils() => _instance;
  SecurityUtils._internal();

  /// Generate secure random string
  static String generateSecureId({int length = 32}) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random.secure();
    return String.fromCharCodes(
      Iterable.generate(length, (_) => chars.codeUnitAt(random.nextInt(chars.length))),
    );
  }

  /// Hash sensitive data
  static String hashData(String data) {
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Validate URL for security
  static bool isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      
      // Only allow HTTPS in production
      if (kReleaseMode && uri.scheme != 'https') {
        return false;
      }
      
      // Allow HTTP only in debug mode
      if (kDebugMode && !['http', 'https'].contains(uri.scheme)) {
        return false;
      }
      
      // Check for valid host
      if (uri.host.isEmpty) {
        return false;
      }
      
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Sanitize user input
  static String sanitizeInput(String input) {
    // Remove potentially dangerous characters
    return input
        .replaceAll(RegExp(r'[<>"]'), '')
        .replaceAll(RegExp(r"'"), '')
        .replaceAll(RegExp(r'javascript:', caseSensitive: false), '')
        .replaceAll(RegExp(r'data:', caseSensitive: false), '')
        .trim();
  }

  /// Validate Arabic text
  static bool isValidArabicText(String text) {
    // Allow Arabic characters, numbers, spaces, and common punctuation
    final arabicRegex = RegExp(r'^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s\d\.\,\!\?\:\;\(\)\[\]\-\+\=\*\/\%\@\#\$\&]*$');
    return arabicRegex.hasMatch(text);
  }

  /// Check if content is appropriate (basic filter)
  static bool isContentAppropriate(String content) {
    // List of inappropriate words (basic implementation)
    final inappropriateWords = [
      // Add inappropriate words here based on your requirements
    ];
    
    final lowerContent = content.toLowerCase();
    
    for (final word in inappropriateWords) {
      if (lowerContent.contains(word.toLowerCase())) {
        return false;
      }
    }
    
    return true;
  }

  /// Validate file extension
  static bool isValidFileExtension(String filename, List<String> allowedExtensions) {
    final extension = filename.split('.').last.toLowerCase();
    return allowedExtensions.contains(extension);
  }

  /// Check if app is running in secure environment
  static bool isSecureEnvironment() {
    // In debug mode, always return true for development
    if (kDebugMode) return true;
    
    // In release mode, perform additional security checks
    // This is a placeholder for more advanced security checks
    return true;
  }

  /// Validate API response structure
  static bool isValidApiResponse(Map<String, dynamic> response) {
    try {
      // Check for required fields
      if (!response.containsKey('data') && !response.containsKey('error')) {
        return false;
      }
      
      // Check for suspicious content
      final responseString = response.toString();
      if (responseString.contains('<script>') || 
          responseString.contains('javascript:') ||
          responseString.contains('data:')) {
        return false;
      }
      
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Rate limiting check
  static bool checkRateLimit(String key, {int maxRequests = 10, Duration window = const Duration(minutes: 1)}) {
    // This is a simplified implementation
    // In a real app, you might use a more sophisticated rate limiting mechanism
    
    // For this example, we'll just return true
    // In a real implementation, you would track requests per key and time windows
    return true;
  }

  /// Encrypt sensitive data (placeholder)
  static String encryptData(String data, String key) {
    // This is a placeholder for actual encryption
    // In a real app, use proper encryption libraries
    final bytes = utf8.encode(data + key);
    final digest = sha256.convert(bytes);
    return base64.encode(digest.bytes);
  }

  /// Decrypt sensitive data (placeholder)
  static String? decryptData(String encryptedData, String key) {
    // This is a placeholder for actual decryption
    // In a real app, use proper decryption libraries
    try {
      final bytes = base64.decode(encryptedData);
      return utf8.decode(bytes);
    } catch (e) {
      return null;
    }
  }

  /// Validate user permissions
  static bool hasPermission(String permission) {
    // Placeholder for permission checking
    // In a real app, check actual device permissions
    return true;
  }

  /// Log security event
  static void logSecurityEvent(String event, {Map<String, dynamic>? details}) {
    if (kDebugMode) {
      print('🔒 SECURITY EVENT: $event');
      if (details != null) {
        print('Details: $details');
      }
    }
    
    // In production, send to security monitoring service
    if (kReleaseMode) {
      _sendToSecurityMonitoring(event, details);
    }
  }

  /// Send security event to monitoring service
  static void _sendToSecurityMonitoring(String event, Map<String, dynamic>? details) {
    // Implement security monitoring service integration
    if (kDebugMode) {
      print('📊 Sending security event to monitoring service: $event');
    }
    try {
      // Create payload for security monitoring
      final payload = {
        'event': event,
        'timestamp': DateTime.now().toIso8601String(),
        'appSignature': generateAppSignature(),
        'details': details ?? {},
        'environment': kReleaseMode ? 'production' : 'development',
      };
      
      // In a real implementation, you would send this to your security monitoring service
      // For example, using Firebase Analytics, Crashlytics, or a custom backend
      
      // Log event for debugging in release mode with limited info
      if (kReleaseMode) {
        print('🔒 Security event logged: ${event.substring(0, min(event.length, 20))}...');
      }
    } catch (e) {
      // Silently handle errors to prevent security monitoring failures from affecting the app
      if (kDebugMode) {
        print('Failed to send security event: $e');
      }
    }
  }

  /// Validate session token (placeholder)
  static bool isValidSessionToken(String token) {
    // Basic validation
    if (token.isEmpty || token.length < 16) {
      return false;
    }
    
    // Check for valid characters
    final validTokenRegex = RegExp(r'^[a-zA-Z0-9\-_\.]+$');
    return validTokenRegex.hasMatch(token);
  }

  /// Generate app signature for integrity check
  static String generateAppSignature() {
    final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    final random = generateSecureId(length: 16);
    final combined = '$timestamp$random';
    return hashData(combined);
  }

  /// Validate app integrity
  static bool validateAppIntegrity() {
    // Placeholder for app integrity checks
    // In a real app, you might check:
    // - App signature
    // - File checksums
    // - Runtime environment
    return true;
  }

  /// Clean up sensitive data from memory
  static void cleanupSensitiveData(String data) {
    // In Dart, we can't directly overwrite memory
    // But we can ensure the string is no longer referenced
    // The garbage collector will handle the cleanup
    
    // Log the cleanup for debugging
    if (kDebugMode) {
      print('🧹 Cleaned up sensitive data');
    }
  }
}
