import 'package:flutter/material.dart';

class ErrorHandler {
  // تسجيل الأخطاء
  static void logError(dynamic error, StackTrace? stackTrace) {
    debugPrint('Error: $error');
    if (stackTrace != null) {
      debugPrint('StackTrace: $stackTrace');
    }
  }

  // معالجة أخطاء API
  static String handleApiError(dynamic error) {
    if (error is Exception) {
      return 'حدث خطأ في الاتصال بالخادم. يرجى المحاولة مرة أخرى.';
    }
    return 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.';
  }

  // معالجة أخطاء التحميل
  static String handleLoadingError(dynamic error) {
    return 'فشل تحميل البيانات. يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى.';
  }
}
