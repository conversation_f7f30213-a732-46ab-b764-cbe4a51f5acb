import 'package:flutter/material.dart';
import 'package:hive/hive.dart';

class SettingsProvider extends ChangeNotifier {
  static const String _isDarkModeKey = 'isDarkMode';
  static const String _fontSizeKey = 'fontSize';
  static const String _selectedReciterKey = 'selectedReciter';
  static const String _notificationsEnabledKey = 'notificationsEnabled';

  Box? _settingsBox;

  // Settings variables
  bool _isDarkMode = false;
  double _fontSize = 18.0;
  int _selectedReciterId = 2; // مشاري راشد العفاسي - رابط مجرب ويعمل
  bool _notificationsEnabled = true;
  bool _autoPlay = false;
  bool _showTranslation = true;
  bool _showTafsir = false;
  TimeOfDay _morningAzkarTime = const TimeOfDay(hour: 6, minute: 0);
  TimeOfDay _eveningAzkarTime = const TimeOfDay(hour: 18, minute: 0);
  TimeOfDay _hadithReminderTime = const TimeOfDay(hour: 9, minute: 0);
  TimeOfDay _quranReminderTime = const TimeOfDay(hour: 20, minute: 0);

  // Getters
  bool get isDarkMode => _isDarkMode;
  double get fontSize => _fontSize;
  int get selectedReciterId => _selectedReciterId;
  bool get notificationsEnabled => _notificationsEnabled;
  bool get autoPlay => _autoPlay;
  bool get showTranslation => _showTranslation;
  bool get showTafsir => _showTafsir;
  TimeOfDay get morningAzkarTime => _morningAzkarTime;
  TimeOfDay get eveningAzkarTime => _eveningAzkarTime;
  TimeOfDay get hadithReminderTime => _hadithReminderTime;
  TimeOfDay get quranReminderTime => _quranReminderTime;

  Future<void> initialize() async {
    try {
      _settingsBox = await Hive.openBox('settings');
      await _loadSettings();
    } catch (e) {
      // If Hive fails, use default values and log error
      print('Settings initialization failed: $e');
      _useDefaultSettings();
      notifyListeners();
    }
  }

  void _useDefaultSettings() {
    _isDarkMode = false;
    _fontSize = 18.0;
    _selectedReciterId = 1;
    _notificationsEnabled = true;
    _autoPlay = false;
    _showTranslation = true;
    _showTafsir = false;
    _morningAzkarTime = const TimeOfDay(hour: 6, minute: 0);
    _eveningAzkarTime = const TimeOfDay(hour: 18, minute: 0);
    _hadithReminderTime = const TimeOfDay(hour: 9, minute: 0);
    _quranReminderTime = const TimeOfDay(hour: 20, minute: 0);
  }

  Future<void> _loadSettings() async {
    try {
      if (_settingsBox != null) {
        _isDarkMode = _settingsBox!.get(_isDarkModeKey, defaultValue: false);
        _fontSize = _settingsBox!.get(_fontSizeKey, defaultValue: 18.0);
        _selectedReciterId = _settingsBox!.get(_selectedReciterKey, defaultValue: 1);
        _notificationsEnabled = _settingsBox!.get(_notificationsEnabledKey, defaultValue: true);
        _autoPlay = _settingsBox!.get('autoPlay', defaultValue: false);
        _showTranslation = _settingsBox!.get('showTranslation', defaultValue: true);
        _showTafsir = _settingsBox!.get('showTafsir', defaultValue: false);

        // Load time settings
        final morningHour = _settingsBox!.get('morningAzkarTime_hour', defaultValue: 6);
        final morningMinute = _settingsBox!.get('morningAzkarTime_minute', defaultValue: 0);
        _morningAzkarTime = TimeOfDay(hour: morningHour, minute: morningMinute);

        final eveningHour = _settingsBox!.get('eveningAzkarTime_hour', defaultValue: 18);
        final eveningMinute = _settingsBox!.get('eveningAzkarTime_minute', defaultValue: 0);
        _eveningAzkarTime = TimeOfDay(hour: eveningHour, minute: eveningMinute);

        final hadithHour = _settingsBox!.get('hadithReminderTime_hour', defaultValue: 9);
        final hadithMinute = _settingsBox!.get('hadithReminderTime_minute', defaultValue: 0);
        _hadithReminderTime = TimeOfDay(hour: hadithHour, minute: hadithMinute);

        final quranHour = _settingsBox!.get('quranReminderTime_hour', defaultValue: 20);
        final quranMinute = _settingsBox!.get('quranReminderTime_minute', defaultValue: 0);
        _quranReminderTime = TimeOfDay(hour: quranHour, minute: quranMinute);
      }
      notifyListeners();
    } catch (e) {
      // Use default values if loading fails
    }
  }

  Future<void> setDarkMode(bool value) async {
    _isDarkMode = value;
    try {
      await _settingsBox?.put(_isDarkModeKey, value);
    } catch (e) {
      // Handle error silently
    }
    notifyListeners();
  }

  Future<void> setFontSize(double value) async {
    _fontSize = value;
    try {
      await _settingsBox?.put(_fontSizeKey, value);
    } catch (e) {
      // Handle error silently
    }
    notifyListeners();
  }

  Future<void> setSelectedReciter(int reciterId) async {
    _selectedReciterId = reciterId;
    try {
      await _settingsBox?.put(_selectedReciterKey, reciterId);
    } catch (e) {
      // Handle error silently
    }
    notifyListeners();
  }

  Future<void> setNotificationsEnabled(bool value) async {
    _notificationsEnabled = value;
    try {
      await _settingsBox?.put(_notificationsEnabledKey, value);
    } catch (e) {
      // Handle error silently
    }
    notifyListeners();
  }

  Future<void> setAutoPlay(bool value) async {
    _autoPlay = value;
    try {
      await _settingsBox?.put('autoPlay', value);
    } catch (e) {
      // Handle error silently
    }
    notifyListeners();
  }

  Future<void> setShowTranslation(bool value) async {
    _showTranslation = value;
    try {
      await _settingsBox?.put('showTranslation', value);
    } catch (e) {
      // Handle error silently
    }
    notifyListeners();
  }

  Future<void> setShowTafsir(bool value) async {
    _showTafsir = value;
    try {
      await _settingsBox?.put('showTafsir', value);
    } catch (e) {
      // Handle error silently
    }
    notifyListeners();
  }

  Future<void> setMorningAzkarTime(TimeOfDay time) async {
    _morningAzkarTime = time;
    try {
      await _settingsBox?.put('morningAzkarTime_hour', time.hour);
      await _settingsBox?.put('morningAzkarTime_minute', time.minute);
    } catch (e) {
      // Handle error silently
    }
    notifyListeners();
  }

  Future<void> setEveningAzkarTime(TimeOfDay time) async {
    _eveningAzkarTime = time;
    try {
      await _settingsBox?.put('eveningAzkarTime_hour', time.hour);
      await _settingsBox?.put('eveningAzkarTime_minute', time.minute);
    } catch (e) {
      // Handle error silently
    }
    notifyListeners();
  }

  Future<void> setHadithReminderTime(TimeOfDay time) async {
    _hadithReminderTime = time;
    try {
      await _settingsBox?.put('hadithReminderTime_hour', time.hour);
      await _settingsBox?.put('hadithReminderTime_minute', time.minute);
    } catch (e) {
      // Handle error silently
    }
    notifyListeners();
  }

  Future<void> setQuranReminderTime(TimeOfDay time) async {
    _quranReminderTime = time;
    try {
      await _settingsBox?.put('quranReminderTime_hour', time.hour);
      await _settingsBox?.put('quranReminderTime_minute', time.minute);
    } catch (e) {
      // Handle error silently
    }
    notifyListeners();
  }

  Future<void> resetToDefaults() async {
    _isDarkMode = false;
    _fontSize = 18.0;
    _selectedReciterId = 1;
    _notificationsEnabled = true;

    try {
      await _settingsBox?.clear();
      await _settingsBox?.put(_isDarkModeKey, _isDarkMode);
      await _settingsBox?.put(_fontSizeKey, _fontSize);
      await _settingsBox?.put(_selectedReciterKey, _selectedReciterId);
      await _settingsBox?.put(_notificationsEnabledKey, _notificationsEnabled);
    } catch (e) {
      // Handle error silently
    }

    notifyListeners();
  }

  // Helper method to get font size category
  String getFontSizeCategory() {
    if (_fontSize <= 14) return 'صغير';
    if (_fontSize <= 18) return 'متوسط';
    if (_fontSize <= 22) return 'كبير';
    return 'كبير جداً';
  }

  // Helper method to get next font size
  double getNextFontSize() {
    if (_fontSize < 14) return 14;
    if (_fontSize < 16) return 16;
    if (_fontSize < 18) return 18;
    if (_fontSize < 20) return 20;
    if (_fontSize < 22) return 22;
    if (_fontSize < 24) return 24;
    return 26;
  }

  // Helper method to get previous font size
  double getPreviousFontSize() {
    if (_fontSize > 24) return 24;
    if (_fontSize > 22) return 22;
    if (_fontSize > 20) return 20;
    if (_fontSize > 18) return 18;
    if (_fontSize > 16) return 16;
    if (_fontSize > 14) return 14;
    return 12;
  }

  // Helper method to format time for display
  String formatTime(TimeOfDay time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }
}
