import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/audio_provider.dart';
import '../providers/quran_provider.dart';
import '../providers/settings_provider.dart';
import '../models/quran_models.dart';

class QuranAudioScreen extends StatefulWidget {
  const QuranAudioScreen({super.key});

  @override
  State<QuranAudioScreen> createState() => _QuranAudioScreenState();
}

class _QuranAudioScreenState extends State<QuranAudioScreen> {
  @override
  void initState() {
    super.initState();
    _initializeAudioProvider();
  }

  void _initializeAudioProvider() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final quranProvider = Provider.of<QuranProvider>(context, listen: false);
      final audioProvider = Provider.of<AudioProvider>(context, listen: false);

      if (quranProvider.surahs.isNotEmpty) {
        audioProvider.initializeSurahs(quranProvider.surahs);
        print('🎵 AudioProvider: Initialized with ${quranProvider.surahs.length} surahs');
      } else {
        // استخدام قائمة السور الثابتة كبديل
        audioProvider.initializeSurahs(surahs);
        print('🎵 AudioProvider: Initialized with ${surahs.length} static surahs');
      }
    });
  }

  final List<Surah> surahs = [
    Surah(number: 1, name: 'الفاتحة', englishName: 'Al-Fatiha', englishNameTranslation: 'The Opening', revelationType: 'مكية', numberOfAyahs: 7, ayahs: []),
    Surah(number: 2, name: 'البقرة', englishName: 'Al-Baqarah', englishNameTranslation: 'The Cow', revelationType: 'مدنية', numberOfAyahs: 286, ayahs: []),
    Surah(number: 3, name: 'آل عمران', englishName: 'Aal-E-Imran', englishNameTranslation: 'The Family of Imran', revelationType: 'مدنية', numberOfAyahs: 200, ayahs: []),
    Surah(number: 4, name: 'النساء', englishName: 'An-Nisa', englishNameTranslation: 'The Women', revelationType: 'مدنية', numberOfAyahs: 176, ayahs: []),
    Surah(number: 5, name: 'المائدة', englishName: 'Al-Maidah', englishNameTranslation: 'The Table', revelationType: 'مدنية', numberOfAyahs: 120, ayahs: []),
    Surah(number: 6, name: 'الأنعام', englishName: 'Al-Anaam', englishNameTranslation: 'The Cattle', revelationType: 'مكية', numberOfAyahs: 165, ayahs: []),
    Surah(number: 7, name: 'الأعراف', englishName: 'Al-Araf', englishNameTranslation: 'The Heights', revelationType: 'مكية', numberOfAyahs: 206, ayahs: []),
    Surah(number: 8, name: 'الأنفال', englishName: 'Al-Anfal', englishNameTranslation: 'The Spoils of War', revelationType: 'مدنية', numberOfAyahs: 75, ayahs: []),
    Surah(number: 9, name: 'التوبة', englishName: 'At-Taubah', englishNameTranslation: 'The Repentance', revelationType: 'مدنية', numberOfAyahs: 129, ayahs: []),
    Surah(number: 10, name: 'يونس', englishName: 'Yunus', englishNameTranslation: 'Jonah', revelationType: 'مكية', numberOfAyahs: 109, ayahs: []),
    Surah(number: 11, name: 'هود', englishName: 'Hud', englishNameTranslation: 'Hud', revelationType: 'مكية', numberOfAyahs: 123, ayahs: []),
    Surah(number: 12, name: 'يوسف', englishName: 'Yusuf', englishNameTranslation: 'Joseph', revelationType: 'مكية', numberOfAyahs: 111, ayahs: []),
    Surah(number: 13, name: 'الرعد', englishName: 'Ar-Rad', englishNameTranslation: 'The Thunder', revelationType: 'مدنية', numberOfAyahs: 43, ayahs: []),
    Surah(number: 14, name: 'إبراهيم', englishName: 'Ibrahim', englishNameTranslation: 'Abraham', revelationType: 'مكية', numberOfAyahs: 52, ayahs: []),
    Surah(number: 15, name: 'الحجر', englishName: 'Al-Hijr', englishNameTranslation: 'The Rocky Tract', revelationType: 'مكية', numberOfAyahs: 99, ayahs: []),
    Surah(number: 16, name: 'النحل', englishName: 'An-Nahl', englishNameTranslation: 'The Bee', revelationType: 'مكية', numberOfAyahs: 128, ayahs: []),
    Surah(number: 17, name: 'الإسراء', englishName: 'Al-Isra', englishNameTranslation: 'The Night Journey', revelationType: 'مكية', numberOfAyahs: 111, ayahs: []),
    Surah(number: 18, name: 'الكهف', englishName: 'Al-Kahf', englishNameTranslation: 'The Cave', revelationType: 'مكية', numberOfAyahs: 110, ayahs: []),
    Surah(number: 19, name: 'مريم', englishName: 'Maryam', englishNameTranslation: 'Mary', revelationType: 'مكية', numberOfAyahs: 98, ayahs: []),
    Surah(number: 20, name: 'طه', englishName: 'Taha', englishNameTranslation: 'Ta-Ha', revelationType: 'مكية', numberOfAyahs: 135, ayahs: []),
    Surah(number: 21, name: 'الأنبياء', englishName: 'Al-Anbiya', englishNameTranslation: 'The Prophets', revelationType: 'مكية', numberOfAyahs: 112, ayahs: []),
    Surah(number: 22, name: 'الحج', englishName: 'Al-Hajj', englishNameTranslation: 'The Pilgrimage', revelationType: 'مدنية', numberOfAyahs: 78, ayahs: []),
    Surah(number: 23, name: 'المؤمنون', englishName: 'Al-Muminoon', englishNameTranslation: 'The Believers', revelationType: 'مكية', numberOfAyahs: 118, ayahs: []),
    Surah(number: 24, name: 'النور', englishName: 'An-Noor', englishNameTranslation: 'The Light', revelationType: 'مدنية', numberOfAyahs: 64, ayahs: []),
    Surah(number: 25, name: 'الفرقان', englishName: 'Al-Furqan', englishNameTranslation: 'The Criterion', revelationType: 'مكية', numberOfAyahs: 77, ayahs: []),
    Surah(number: 26, name: 'الشعراء', englishName: 'Ash-Shuara', englishNameTranslation: 'The Poets', revelationType: 'مكية', numberOfAyahs: 227, ayahs: []),
    Surah(number: 27, name: 'النمل', englishName: 'An-Naml', englishNameTranslation: 'The Ant', revelationType: 'مكية', numberOfAyahs: 93, ayahs: []),
    Surah(number: 28, name: 'القصص', englishName: 'Al-Qasas', englishNameTranslation: 'The Stories', revelationType: 'مكية', numberOfAyahs: 88, ayahs: []),
    Surah(number: 29, name: 'العنكبوت', englishName: 'Al-Ankabut', englishNameTranslation: 'The Spider', revelationType: 'مكية', numberOfAyahs: 69, ayahs: []),
    Surah(number: 30, name: 'الروم', englishName: 'Ar-Room', englishNameTranslation: 'The Romans', revelationType: 'مكية', numberOfAyahs: 60, ayahs: []),
    Surah(number: 31, name: 'لقمان', englishName: 'Luqman', englishNameTranslation: 'Luqman', revelationType: 'مكية', numberOfAyahs: 34, ayahs: []),
    Surah(number: 32, name: 'السجدة', englishName: 'As-Sajdah', englishNameTranslation: 'The Prostration', revelationType: 'مكية', numberOfAyahs: 30, ayahs: []),
    Surah(number: 33, name: 'الأحزاب', englishName: 'Al-Ahzab', englishNameTranslation: 'The Clans', revelationType: 'مدنية', numberOfAyahs: 73, ayahs: []),
    Surah(number: 34, name: 'سبأ', englishName: 'Saba', englishNameTranslation: 'Sheba', revelationType: 'مكية', numberOfAyahs: 54, ayahs: []),
    Surah(number: 35, name: 'فاطر', englishName: 'Fatir', englishNameTranslation: 'The Creator', revelationType: 'مكية', numberOfAyahs: 45, ayahs: []),
    Surah(number: 36, name: 'يس', englishName: 'Yasin', englishNameTranslation: 'Ya Sin', revelationType: 'مكية', numberOfAyahs: 83, ayahs: []),
    Surah(number: 37, name: 'الصافات', englishName: 'As-Saffat', englishNameTranslation: 'Those who set the Ranks', revelationType: 'مكية', numberOfAyahs: 182, ayahs: []),
    Surah(number: 38, name: 'ص', englishName: 'Sad', englishNameTranslation: 'The Letter Sad', revelationType: 'مكية', numberOfAyahs: 88, ayahs: []),
    Surah(number: 39, name: 'الزمر', englishName: 'Az-Zumar', englishNameTranslation: 'The Troops', revelationType: 'مكية', numberOfAyahs: 75, ayahs: []),
    Surah(number: 40, name: 'غافر', englishName: 'Ghafir', englishNameTranslation: 'The Forgiver', revelationType: 'مكية', numberOfAyahs: 85, ayahs: []),
    Surah(number: 41, name: 'فصلت', englishName: 'Fussilat', englishNameTranslation: 'Explained in Detail', revelationType: 'مكية', numberOfAyahs: 54, ayahs: []),
    Surah(number: 42, name: 'الشورى', englishName: 'Ash-Shura', englishNameTranslation: 'The Consultation', revelationType: 'مكية', numberOfAyahs: 53, ayahs: []),
    Surah(number: 43, name: 'الزخرف', englishName: 'Az-Zukhruf', englishNameTranslation: 'The Ornaments of Gold', revelationType: 'مكية', numberOfAyahs: 89, ayahs: []),
    Surah(number: 44, name: 'الدخان', englishName: 'Ad-Dukhan', englishNameTranslation: 'The Smoke', revelationType: 'مكية', numberOfAyahs: 59, ayahs: []),
    Surah(number: 45, name: 'الجاثية', englishName: 'Al-Jathiya', englishNameTranslation: 'The Crouching', revelationType: 'مكية', numberOfAyahs: 37, ayahs: []),
    Surah(number: 46, name: 'الأحقاف', englishName: 'Al-Ahqaf', englishNameTranslation: 'The Wind-Curved Sandhills', revelationType: 'مكية', numberOfAyahs: 35, ayahs: []),
    Surah(number: 47, name: 'محمد', englishName: 'Muhammad', englishNameTranslation: 'Muhammad', revelationType: 'مدنية', numberOfAyahs: 38, ayahs: []),
    Surah(number: 48, name: 'الفتح', englishName: 'Al-Fath', englishNameTranslation: 'The Victory', revelationType: 'مدنية', numberOfAyahs: 29, ayahs: []),
    Surah(number: 49, name: 'الحجرات', englishName: 'Al-Hujraat', englishNameTranslation: 'The Rooms', revelationType: 'مدنية', numberOfAyahs: 18, ayahs: []),
    Surah(number: 50, name: 'ق', englishName: 'Qaf', englishNameTranslation: 'The Letter Qaf', revelationType: 'مكية', numberOfAyahs: 45, ayahs: []),
    Surah(number: 51, name: 'الذاريات', englishName: 'Adh-Dhariyat', englishNameTranslation: 'The Winnowing Winds', revelationType: 'مكية', numberOfAyahs: 60, ayahs: []),
    Surah(number: 52, name: 'الطور', englishName: 'At-Tur', englishNameTranslation: 'The Mount', revelationType: 'مكية', numberOfAyahs: 49, ayahs: []),
    Surah(number: 53, name: 'النجم', englishName: 'An-Najm', englishNameTranslation: 'The Star', revelationType: 'مكية', numberOfAyahs: 62, ayahs: []),
    Surah(number: 54, name: 'القمر', englishName: 'Al-Qamar', englishNameTranslation: 'The Moon', revelationType: 'مكية', numberOfAyahs: 55, ayahs: []),
    Surah(number: 55, name: 'الرحمن', englishName: 'Ar-Rahman', englishNameTranslation: 'The Beneficent', revelationType: 'مكية', numberOfAyahs: 78, ayahs: []),
    Surah(number: 56, name: 'الواقعة', englishName: 'Al-Waqiah', englishNameTranslation: 'The Inevitable', revelationType: 'مكية', numberOfAyahs: 96, ayahs: []),
    Surah(number: 57, name: 'الحديد', englishName: 'Al-Hadid', englishNameTranslation: 'The Iron', revelationType: 'مدنية', numberOfAyahs: 29, ayahs: []),
    Surah(number: 58, name: 'المجادلة', englishName: 'Al-Mujadila', englishNameTranslation: 'The Pleading Woman', revelationType: 'مدنية', numberOfAyahs: 22, ayahs: []),
    Surah(number: 59, name: 'الحشر', englishName: 'Al-Hashr', englishNameTranslation: 'The Exile', revelationType: 'مدنية', numberOfAyahs: 24, ayahs: []),
    Surah(number: 60, name: 'الممتحنة', englishName: 'Al-Mumtahina', englishNameTranslation: 'She that is to be examined', revelationType: 'مدنية', numberOfAyahs: 13, ayahs: []),
    Surah(number: 61, name: 'الصف', englishName: 'As-Saff', englishNameTranslation: 'The Ranks', revelationType: 'مدنية', numberOfAyahs: 14, ayahs: []),
    Surah(number: 62, name: 'الجمعة', englishName: 'Al-Jumua', englishNameTranslation: 'The Congregation', revelationType: 'مدنية', numberOfAyahs: 11, ayahs: []),
    Surah(number: 63, name: 'المنافقون', englishName: 'Al-Munafiqoon', englishNameTranslation: 'The Hypocrites', revelationType: 'مدنية', numberOfAyahs: 11, ayahs: []),
    Surah(number: 64, name: 'التغابن', englishName: 'At-Taghabun', englishNameTranslation: 'The Mutual Disillusion', revelationType: 'مدنية', numberOfAyahs: 18, ayahs: []),
    Surah(number: 65, name: 'الطلاق', englishName: 'At-Talaq', englishNameTranslation: 'The Divorce', revelationType: 'مدنية', numberOfAyahs: 12, ayahs: []),
    Surah(number: 66, name: 'التحريم', englishName: 'At-Tahrim', englishNameTranslation: 'The Prohibition', revelationType: 'مدنية', numberOfAyahs: 12, ayahs: []),
    Surah(number: 67, name: 'الملك', englishName: 'Al-Mulk', englishNameTranslation: 'The Sovereignty', revelationType: 'مكية', numberOfAyahs: 30, ayahs: []),
    Surah(number: 68, name: 'القلم', englishName: 'Al-Qalam', englishNameTranslation: 'The Pen', revelationType: 'مكية', numberOfAyahs: 52, ayahs: []),
    Surah(number: 69, name: 'الحاقة', englishName: 'Al-Haaqqa', englishNameTranslation: 'The Reality', revelationType: 'مكية', numberOfAyahs: 52, ayahs: []),
    Surah(number: 70, name: 'المعارج', englishName: 'Al-Maarij', englishNameTranslation: 'The Ascending Stairways', revelationType: 'مكية', numberOfAyahs: 44, ayahs: []),
    Surah(number: 71, name: 'نوح', englishName: 'Nuh', englishNameTranslation: 'Noah', revelationType: 'مكية', numberOfAyahs: 28, ayahs: []),
    Surah(number: 72, name: 'الجن', englishName: 'Al-Jinn', englishNameTranslation: 'The Jinn', revelationType: 'مكية', numberOfAyahs: 28, ayahs: []),
    Surah(number: 73, name: 'المزمل', englishName: 'Al-Muzzammil', englishNameTranslation: 'The Enshrouded One', revelationType: 'مكية', numberOfAyahs: 20, ayahs: []),
    Surah(number: 74, name: 'المدثر', englishName: 'Al-Muddaththir', englishNameTranslation: 'The Cloaked One', revelationType: 'مكية', numberOfAyahs: 56, ayahs: []),
    Surah(number: 75, name: 'القيامة', englishName: 'Al-Qiyama', englishNameTranslation: 'The Resurrection', revelationType: 'مكية', numberOfAyahs: 40, ayahs: []),
    Surah(number: 76, name: 'الإنسان', englishName: 'Al-Insan', englishNameTranslation: 'The Man', revelationType: 'مدنية', numberOfAyahs: 31, ayahs: []),
    Surah(number: 77, name: 'المرسلات', englishName: 'Al-Mursalat', englishNameTranslation: 'The Emissaries', revelationType: 'مكية', numberOfAyahs: 50, ayahs: []),
    Surah(number: 78, name: 'النبأ', englishName: 'An-Naba', englishNameTranslation: 'The Tidings', revelationType: 'مكية', numberOfAyahs: 40, ayahs: []),
    Surah(number: 79, name: 'النازعات', englishName: 'An-Naziat', englishNameTranslation: 'Those who drag forth', revelationType: 'مكية', numberOfAyahs: 46, ayahs: []),
    Surah(number: 80, name: 'عبس', englishName: 'Abasa', englishNameTranslation: 'He Frowned', revelationType: 'مكية', numberOfAyahs: 42, ayahs: []),
    Surah(number: 81, name: 'التكوير', englishName: 'At-Takwir', englishNameTranslation: 'The Overthrowing', revelationType: 'مكية', numberOfAyahs: 29, ayahs: []),
    Surah(number: 82, name: 'الانفطار', englishName: 'Al-Infitar', englishNameTranslation: 'The Cleaving', revelationType: 'مكية', numberOfAyahs: 19, ayahs: []),
    Surah(number: 83, name: 'المطففين', englishName: 'Al-Mutaffifin', englishNameTranslation: 'The Defrauding', revelationType: 'مكية', numberOfAyahs: 36, ayahs: []),
    Surah(number: 84, name: 'الانشقاق', englishName: 'Al-Inshiqaq', englishNameTranslation: 'The Splitting Open', revelationType: 'مكية', numberOfAyahs: 25, ayahs: []),
    Surah(number: 85, name: 'البروج', englishName: 'Al-Buruj', englishNameTranslation: 'The Mansions of the Stars', revelationType: 'مكية', numberOfAyahs: 22, ayahs: []),
    Surah(number: 86, name: 'الطارق', englishName: 'At-Tariq', englishNameTranslation: 'The Morning Star', revelationType: 'مكية', numberOfAyahs: 17, ayahs: []),
    Surah(number: 87, name: 'الأعلى', englishName: 'Al-Ala', englishNameTranslation: 'The Most High', revelationType: 'مكية', numberOfAyahs: 19, ayahs: []),
    Surah(number: 88, name: 'الغاشية', englishName: 'Al-Ghashiya', englishNameTranslation: 'The Overwhelming', revelationType: 'مكية', numberOfAyahs: 26, ayahs: []),
    Surah(number: 89, name: 'الفجر', englishName: 'Al-Fajr', englishNameTranslation: 'The Dawn', revelationType: 'مكية', numberOfAyahs: 30, ayahs: []),
    Surah(number: 90, name: 'البلد', englishName: 'Al-Balad', englishNameTranslation: 'The City', revelationType: 'مكية', numberOfAyahs: 20, ayahs: []),
    Surah(number: 91, name: 'الشمس', englishName: 'Ash-Shams', englishNameTranslation: 'The Sun', revelationType: 'مكية', numberOfAyahs: 15, ayahs: []),
    Surah(number: 92, name: 'الليل', englishName: 'Al-Layl', englishNameTranslation: 'The Night', revelationType: 'مكية', numberOfAyahs: 21, ayahs: []),
    Surah(number: 93, name: 'الضحى', englishName: 'Ad-Dhuha', englishNameTranslation: 'The Morning Hours', revelationType: 'مكية', numberOfAyahs: 11, ayahs: []),
    Surah(number: 94, name: 'الشرح', englishName: 'Al-Inshirah', englishNameTranslation: 'The Relief', revelationType: 'مكية', numberOfAyahs: 8, ayahs: []),
    Surah(number: 95, name: 'التين', englishName: 'At-Tin', englishNameTranslation: 'The Fig', revelationType: 'مكية', numberOfAyahs: 8, ayahs: []),
    Surah(number: 96, name: 'العلق', englishName: 'Al-Alaq', englishNameTranslation: 'The Clot', revelationType: 'مكية', numberOfAyahs: 19, ayahs: []),
    Surah(number: 97, name: 'القدر', englishName: 'Al-Qadr', englishNameTranslation: 'The Power', revelationType: 'مكية', numberOfAyahs: 5, ayahs: []),
    Surah(number: 98, name: 'البينة', englishName: 'Al-Bayyina', englishNameTranslation: 'The Evidence', revelationType: 'مدنية', numberOfAyahs: 8, ayahs: []),
    Surah(number: 99, name: 'الزلزلة', englishName: 'Az-Zalzala', englishNameTranslation: 'The Earthquake', revelationType: 'مدنية', numberOfAyahs: 8, ayahs: []),
    Surah(number: 100, name: 'العاديات', englishName: 'Al-Adiyat', englishNameTranslation: 'The Courser', revelationType: 'مكية', numberOfAyahs: 11, ayahs: []),
    Surah(number: 101, name: 'القارعة', englishName: 'Al-Qaria', englishNameTranslation: 'The Calamity', revelationType: 'مكية', numberOfAyahs: 11, ayahs: []),
    Surah(number: 102, name: 'التكاثر', englishName: 'At-Takathur', englishNameTranslation: 'The Rivalry in world increase', revelationType: 'مكية', numberOfAyahs: 8, ayahs: []),
    Surah(number: 103, name: 'العصر', englishName: 'Al-Asr', englishNameTranslation: 'The Declining Day', revelationType: 'مكية', numberOfAyahs: 3, ayahs: []),
    Surah(number: 104, name: 'الهمزة', englishName: 'Al-Humaza', englishNameTranslation: 'The Traducer', revelationType: 'مكية', numberOfAyahs: 9, ayahs: []),
    Surah(number: 105, name: 'الفيل', englishName: 'Al-Fil', englishNameTranslation: 'The Elephant', revelationType: 'مكية', numberOfAyahs: 5, ayahs: []),
    Surah(number: 106, name: 'قريش', englishName: 'Quraish', englishNameTranslation: 'Quraysh', revelationType: 'مكية', numberOfAyahs: 4, ayahs: []),
    Surah(number: 107, name: 'الماعون', englishName: 'Al-Maun', englishNameTranslation: 'The Small kindnesses', revelationType: 'مكية', numberOfAyahs: 7, ayahs: []),
    Surah(number: 108, name: 'الكوثر', englishName: 'Al-Kawthar', englishNameTranslation: 'The Abundance', revelationType: 'مكية', numberOfAyahs: 3, ayahs: []),
    Surah(number: 109, name: 'الكافرون', englishName: 'Al-Kafiroon', englishNameTranslation: 'The Disbelievers', revelationType: 'مكية', numberOfAyahs: 6, ayahs: []),
    Surah(number: 110, name: 'النصر', englishName: 'An-Nasr', englishNameTranslation: 'The Divine Support', revelationType: 'مدنية', numberOfAyahs: 3, ayahs: []),
    Surah(number: 111, name: 'المسد', englishName: 'Al-Masad', englishNameTranslation: 'The Palm Fibre', revelationType: 'مكية', numberOfAyahs: 5, ayahs: []),
    Surah(number: 112, name: 'الإخلاص', englishName: 'Al-Ikhlas', englishNameTranslation: 'The Sincerity', revelationType: 'مكية', numberOfAyahs: 4, ayahs: []),
    Surah(number: 113, name: 'الفلق', englishName: 'Al-Falaq', englishNameTranslation: 'The Daybreak', revelationType: 'مكية', numberOfAyahs: 5, ayahs: []),
    Surah(number: 114, name: 'الناس', englishName: 'An-Nas', englishNameTranslation: 'Mankind', revelationType: 'مكية', numberOfAyahs: 6, ayahs: []),
  ];

  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  List<Surah> get filteredSurahs {
    if (_searchQuery.isEmpty) {
      return surahs;
    }
    return surahs.where((surah) {
      return surah.name.contains(_searchQuery) ||
             surah.englishName.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تشغيل القرآن الكريم'),
        backgroundColor: const Color(0xFF4CAF50),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          Consumer<SettingsProvider>(
            builder: (context, settings, child) {
              return IconButton(
                icon: const Icon(Icons.person_rounded),
                onPressed: () => _showReciterSelector(context),
                tooltip: 'اختيار القارئ',
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [Color(0xFF4CAF50), Color(0xFF66BB6A)],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: TextField(
                  controller: _searchController,
                  decoration: const InputDecoration(
                    hintText: 'ابحث عن السورة...',
                    prefixIcon: Icon(Icons.search_rounded, color: Color(0xFF4CAF50)),
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
              ),
            ),
          ),

          // قائمة السور
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: filteredSurahs.length,
              itemBuilder: (context, index) {
                final surah = filteredSurahs[index];
                return _buildSurahCard(context, surah);
              },
            ),
          ),

        ],
      ),
    );
  }

  Widget _buildSurahCard(BuildContext context, Surah surah) {
    return Consumer<AudioProvider>(
      builder: (context, audioProvider, child) {
        final isCurrentSurah = audioProvider.currentSurah?.number == surah.number;
        final isPlaying = isCurrentSurah && audioProvider.isPlaying;

        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          decoration: BoxDecoration(
            color: isCurrentSurah
              ? const Color(0xFF4CAF50).withValues(alpha: 0.1)
              : Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: isCurrentSurah
                ? const Color(0xFF4CAF50)
                : Colors.grey.shade200,
              width: isCurrentSurah ? 2 : 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ListTile(
            contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),

            // رقم السورة
            leading: Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: isCurrentSurah
                    ? [const Color(0xFF4CAF50), const Color(0xFF66BB6A)]
                    : [Colors.grey.shade300, Colors.grey.shade400],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Center(
                child: Text(
                  '${surah.number}',
                  style: TextStyle(
                    color: isCurrentSurah ? Colors.white : Colors.grey.shade700,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),

            // معلومات السورة
            title: Text(
              surah.name,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: isCurrentSurah ? const Color(0xFF4CAF50) : Colors.black87,
              ),
            ),
            subtitle: Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: surah.revelationType == 'مكية'
                        ? Colors.orange.shade100
                        : Colors.blue.shade100,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      surah.revelationType,
                      style: TextStyle(
                        fontSize: 12,
                        color: surah.revelationType == 'مكية'
                          ? Colors.orange.shade700
                          : Colors.blue.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '${surah.numberOfAyahs} آية',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),

            // زر التشغيل
            trailing: Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: isCurrentSurah
                    ? [const Color(0xFF4CAF50), const Color(0xFF66BB6A)]
                    : [Colors.grey.shade200, Colors.grey.shade300],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: (isCurrentSurah ? const Color(0xFF4CAF50) : Colors.grey)
                        .withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: IconButton(
                icon: Icon(
                  isPlaying ? Icons.pause_rounded : Icons.play_arrow_rounded,
                  color: isCurrentSurah ? Colors.white : Colors.grey.shade600,
                  size: 24,
                ),
                onPressed: () => _handleSurahTap(context, surah, audioProvider),
              ),
            ),

            // النقر على السورة
            onTap: () => _handleSurahTap(context, surah, audioProvider),
          ),
        );
      },
    );
  }

  void _handleSurahTap(BuildContext context, Surah surah, AudioProvider audioProvider) {
    print('🎵 UI: Surah tapped: ${surah.name}');

    // تهيئة قائمة السور في AudioProvider إذا لم تكن مهيأة
    final quranProvider = Provider.of<QuranProvider>(context, listen: false);
    if (quranProvider.surahs.isNotEmpty) {
      audioProvider.initializeSurahs(quranProvider.surahs);
    }

    if (audioProvider.currentSurah?.number == surah.number) {
      // نفس السورة - تبديل التشغيل/الإيقاف
      if (audioProvider.isPlaying) {
        audioProvider.pause();
      } else {
        audioProvider.resume();
      }
    } else {
      // سورة جديدة - بدء التشغيل
      final settings = Provider.of<SettingsProvider>(context, listen: false);

      // قائمة القراء المتاحين
      final availableReciters = [
        Reciter(id: 1, name: 'Mishary Rashid Alafasy', arabicName: 'مشاري راشد العفاسي', style: 'مرتل', baseUrl: 'https://server8.mp3quran.net/afs/'),
        Reciter(id: 2, name: 'Abdul Basit Abdul Samad', arabicName: 'عبد الباسط عبد الصمد', style: 'مرتل', baseUrl: 'https://server7.mp3quran.net/basit/Alafasy_128_kbps/'),
        Reciter(id: 3, name: 'Saad Al Ghamdi', arabicName: 'سعد الغامدي', style: 'مرتل', baseUrl: 'https://server7.mp3quran.net/s_gmd/'),
        Reciter(id: 4, name: 'Ahmed Al Ajmy', arabicName: 'أحمد العجمي', style: 'مرتل', baseUrl: 'https://server10.mp3quran.net/ajm/'),
        Reciter(id: 5, name: 'Maher Al Mueaqly', arabicName: 'ماهر المعيقلي', style: 'مرتل', baseUrl: 'https://server12.mp3quran.net/maher/'),
      ];

      // العثور على القارئ المحدد أو استخدام الافتراضي
      final selectedReciter = availableReciters.firstWhere(
        (reciter) => reciter.id == settings.selectedReciterId,
        orElse: () => availableReciters.first, // مشاري العفاسي كافتراضي
      );

      audioProvider.playSurah(surah, selectedReciter);
    }
  }

  void _showReciterSelector(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            // مقبض السحب
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // العنوان
            const Padding(
              padding: EdgeInsets.all(16),
              child: Text(
                'اختر القارئ',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

            // قائمة القراء
            Expanded(
              child: Consumer<SettingsProvider>(
                builder: (context, settings, child) {
                  // استخدام نفس قائمة القراء من SettingsProvider
                  final reciters = [
                    Reciter(id: 1, name: 'Mishary Rashid Alafasy', arabicName: 'مشاري راشد العفاسي', style: 'مرتل', baseUrl: 'https://server8.mp3quran.net/afs/'),
                    Reciter(id: 2, name: 'Abdul Basit Abdul Samad', arabicName: 'عبد الباسط عبد الصمد', style: 'مرتل', baseUrl: 'https://server7.mp3quran.net/basit/Alafasy_128_kbps/'),
                    Reciter(id: 3, name: 'Saad Al Ghamdi', arabicName: 'سعد الغامدي', style: 'مرتل', baseUrl: 'https://server7.mp3quran.net/s_gmd/'),
                    Reciter(id: 4, name: 'Ahmed Al Ajmy', arabicName: 'أحمد العجمي', style: 'مرتل', baseUrl: 'https://server10.mp3quran.net/ajm/'),
                    Reciter(id: 5, name: 'Maher Al Mueaqly', arabicName: 'ماهر المعيقلي', style: 'مرتل', baseUrl: 'https://server12.mp3quran.net/maher/'),
                  ];

                  return ListView.builder(
                    itemCount: reciters.length,
                    itemBuilder: (context, index) {
                      final reciter = reciters[index];
                      final isSelected = settings.selectedReciterId == reciter.id;

                      return ListTile(
                        leading: CircleAvatar(
                          backgroundColor: isSelected
                            ? const Color(0xFF4CAF50)
                            : Colors.grey.shade200,
                          child: Icon(
                            Icons.person_rounded,
                            color: isSelected ? Colors.white : Colors.grey,
                          ),
                        ),
                        title: Text(
                          reciter.arabicName,
                          style: TextStyle(
                            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                          ),
                        ),
                        subtitle: Text(reciter.style),
                        trailing: isSelected
                          ? const Icon(Icons.check_rounded, color: Color(0xFF4CAF50))
                          : null,
                        onTap: () {
                          settings.setSelectedReciter(reciter.id);
                          Navigator.pop(context);

                          // إظهار رسالة تأكيد
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('تم اختيار القارئ: ${reciter.arabicName}'),
                              backgroundColor: const Color(0xFF4CAF50),
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        },
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}