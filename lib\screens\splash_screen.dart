import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../services/database_service.dart';
import '../services/audio_service.dart';
import '../services/notification_service.dart';
import '../providers/settings_provider.dart';
import '../providers/quran_provider.dart';
import '../providers/azkar_provider.dart';
import '../providers/hadith_provider.dart';
import 'home_screen.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  
  String _currentStatus = 'جاري التحميل...';
  double _progress = 0.0;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _initializeApp();
  }

  void _setupAnimations() {
    _controller = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: const Interval(0.0, 0.5, curve: Curves.easeIn),
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: const Interval(0.2, 0.8, curve: Curves.elasticOut),
    ));

    _controller.forward();
  }

  Future<void> _initializeApp() async {
    try {
      print('🚀 App: Starting initialization...');
      
      // تحديث الحالة والتقدم
      _updateStatus('تهيئة قاعدة البيانات...', 0.1);
      await DatabaseService.instance.initializeDatabase();
      print('✅ App: Database initialized');

      _updateStatus('تهيئة خدمة الصوت...', 0.3);
      await AudioService.instance.initialize();
      print('✅ App: Audio service initialized');

      _updateStatus('تهيئة الإشعارات...', 0.5);
      await NotificationService.instance.initialize();
      print('✅ App: Notification service initialized');

      _updateStatus('تحميل الإعدادات...', 0.7);
      if (mounted) {
        final settingsProvider = Provider.of<SettingsProvider>(context, listen: false);
        await settingsProvider.initialize();
        print('✅ App: Settings initialized');
      }

      _updateStatus('تحميل البيانات الأساسية...', 0.9);
      if (mounted) {
        await _loadEssentialData();
      }

      // تعيين اتجاهات الشاشة
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);

      _updateStatus('مكتمل!', 1.0);
      
      // انتظار انتهاء الأنيميشن
      await Future.delayed(const Duration(milliseconds: 500));
      
      if (mounted) {
        Navigator.of(context).pushReplacement(
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) => const HomeScreen(),
            transitionsBuilder: (context, animation, secondaryAnimation, child) {
              return FadeTransition(opacity: animation, child: child);
            },
            transitionDuration: const Duration(milliseconds: 500),
          ),
        );
      }

      print('🎉 App: All services initialized successfully');
    } catch (e) {
      print('❌ App: Error during initialization: $e');
      _updateStatus('حدث خطأ في التحميل', 1.0);
      
      // الانتقال للتطبيق حتى لو حدث خطأ
      await Future.delayed(const Duration(seconds: 2));
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const HomeScreen()),
        );
      }
    }
  }

  Future<void> _loadEssentialData() async {
    if (!mounted) return;
    
    try {
      final quranProvider = Provider.of<QuranProvider>(context, listen: false);
      final azkarProvider = Provider.of<AzkarProvider>(context, listen: false);
      final hadithProvider = Provider.of<HadithProvider>(context, listen: false);

      // تحميل البيانات الأساسية فقط
      await Future.wait([
        quranProvider.initialize(),
        azkarProvider.initialize(),
        hadithProvider.initialize(),
      ]);
    } catch (e) {
      print('❌ Error loading essential data: $e');
    }
  }

  void _updateStatus(String status, double progress) {
    if (mounted) {
      setState(() {
        _currentStatus = status;
        _progress = progress;
      });
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).primaryColor,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context).primaryColor,
              Theme.of(context).primaryColor.withValues(alpha: 0.8),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              Expanded(
                flex: 3,
                child: Center(
                  child: AnimatedBuilder(
                    animation: _controller,
                    builder: (context, child) {
                      return FadeTransition(
                        opacity: _fadeAnimation,
                        child: ScaleTransition(
                          scale: _scaleAnimation,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              // شعار التطبيق
                              Container(
                                width: 120,
                                height: 120,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(30),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withValues(alpha: 0.2),
                                      blurRadius: 20,
                                      offset: const Offset(0, 10),
                                    ),
                                  ],
                                ),
                                child: const Icon(
                                  Icons.book,
                                  size: 60,
                                  color: Color(0xFF2E7D32),
                                ),
                              ),
                              const SizedBox(height: 30),
                              
                              // اسم التطبيق
                              const Text(
                                'قرآني',
                                style: TextStyle(
                                  fontSize: 36,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                              const SizedBox(height: 10),
                              
                              // وصف التطبيق
                              const Text(
                                'القرآن الكريم والأذكار والأحاديث',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.white70,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 20),
                              
                              // آية قرآنية
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 20,
                                  vertical: 15,
                                ),
                                margin: const EdgeInsets.symmetric(horizontal: 40),
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(15),
                                  border: Border.all(
                                    color: Colors.white.withValues(alpha: 0.3),
                                    width: 1,
                                  ),
                                ),
                                child: const Text(
                                  '﴿وَقُلْ رَبِّ زِدْنِي عِلْمًا﴾',
                                  style: TextStyle(
                                    fontSize: 20,
                                    color: Colors.white,
                                    fontWeight: FontWeight.w500,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
              
              // منطقة التحميل
              Expanded(
                flex: 1,
                child: Padding(
                  padding: const EdgeInsets.all(40),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // شريط التقدم
                      Container(
                        width: double.infinity,
                        height: 6,
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(3),
                        ),
                        child: FractionallySizedBox(
                          alignment: Alignment.centerLeft,
                          widthFactor: _progress,
                          child: Container(
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(3),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 20),
                      
                      // حالة التحميل
                      Text(
                        _currentStatus,
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.white70,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 10),
                      
                      // نسبة التقدم
                      Text(
                        '${(_progress * 100).toInt()}%',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.white60,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}