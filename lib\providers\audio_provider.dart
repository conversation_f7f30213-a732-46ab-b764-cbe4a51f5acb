import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import '../models/quran_models.dart';
import '../services/audio_service.dart';

class AudioProvider extends ChangeNotifier {
  static final AudioProvider _instance = AudioProvider._internal();
  factory AudioProvider() => _instance;
  
  static const String _historyBoxName = 'audio_history';
  static const String _favoritesBoxName = 'audio_favorites';
  Box? _historyBox;
  Box? _favoritesBox;
  
  AudioProvider._internal() {
    _initializeAudioService();
    _initializeHive();
  }

  Future<void> _initializeHive() async {
    try {
      _historyBox = await Hive.openBox(_historyBoxName);
      _favoritesBox = await Hive.openBox(_favoritesBoxName);
      _loadHistory();
      _loadFavorites();
    } catch (e) {
      print('Hive initialization failed: $e');
    }
  }

  void _loadHistory() {
    _listeningHistory = _historyBox?.values.toList().cast<Map<String, dynamic>>() ?? [];
    notifyListeners();
  }

  void _loadFavorites() {
    _favorites = _favoritesBox?.values.toList().cast<Map<String, dynamic>>() ?? [];
    notifyListeners();
  }

  Future<void> addToHistory(Surah surah, Reciter reciter) async {
    final entry = {
      'surahId': surah.number, // استخدم number بدلاً من id
      'surahName': surah.name,
      'reciterId': reciter.id,
      'reciterName': reciter.arabicName,
      'timestamp': DateTime.now().toIso8601String(),
    };
    
    await _historyBox?.add(entry);
    _listeningHistory.insert(0, entry);
    notifyListeners();
  }

  Future<void> toggleFavorite(Surah surah, Reciter reciter) async {
    final existingIndex = _favorites.indexWhere(
      (fav) => fav['surahId'] == surah.number && fav['reciterId'] == reciter.id
    );
    
    if (existingIndex >= 0) {
      await _favoritesBox?.deleteAt(existingIndex);
      _favorites.removeAt(existingIndex);
    } else {
      final entry = {
        'surahId': surah.number, // استخدم number بدلاً من id
        'surahName': surah.name,
        'reciterId': reciter.id,
        'reciterName': reciter.arabicName,
        'timestamp': DateTime.now().toIso8601String(),
      };
      await _favoritesBox?.add(entry);
      _favorites.insert(0, entry);
    }
    notifyListeners();
  }

  // Audio state
  bool _isPlaying = false;
  bool _isPaused = false;
  double _progress = 0.0;
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;

  // Current playing content
  Surah? _currentSurah;
  Reciter? _currentReciter;
  List<Surah> _allSurahs = [];
  int _currentSurahIndex = 0;
  
  // History and favorites
  List<Map<String, dynamic>> _listeningHistory = [];
  List<Map<String, dynamic>> _favorites = [];
  
  // Getters for history and favorites
  List<Map<String, dynamic>> get listeningHistory => List.unmodifiable(_listeningHistory);
  List<Map<String, dynamic>> get favorites => List.unmodifiable(_favorites);
  
  bool isFavorite(Surah surah, Reciter reciter) {
    return _favorites.any(
      (fav) => fav['surahId'] == surah.number && fav['reciterId'] == reciter.id
    );
  }

  // Getters
  bool get isPlaying => _isPlaying;
  bool get isPaused => _isPaused;
  double get progress => _progress;
  Duration get currentPosition => _currentPosition;
  Duration get totalDuration => _totalDuration;
  Surah? get currentSurah => _currentSurah;
  Reciter? get currentReciter => _currentReciter;

  bool get canGoPrevious => _currentSurahIndex > 0;
  bool get canGoNext => _currentSurahIndex < _allSurahs.length - 1;

  // Initialize AudioService
  void _initializeAudioService() {
    final audioService = AudioService.instance;

    // Setup callbacks to sync with AudioService
    audioService.onPlayingStateChanged = (isPlaying) {
      _isPlaying = isPlaying;
      _isPaused = !isPlaying && audioService.isPaused;
      notifyListeners();
    };

    audioService.onPositionChanged = (position) {
      _currentPosition = position;
      if (_totalDuration.inMilliseconds > 0) {
        _progress = position.inMilliseconds / _totalDuration.inMilliseconds;
      }
      notifyListeners();
    };

    audioService.onDurationChanged = (duration) {
      _totalDuration = duration;
      notifyListeners();
    };

    audioService.onCompleted = () {
      onPlaybackCompleted();
    };

    // Initialize the service
    audioService.initialize();
  }

  // Initialize with surahs list
  void initializeSurahs(List<Surah> surahs) {
    _allSurahs = surahs;
    notifyListeners();
  }

  // Play surah
  Future<void> playSurah(Surah surah, Reciter reciter) async {
    try {
      print('🎵 AudioProvider: Starting playback for ${surah.name}');

      _currentSurah = surah;
      _currentReciter = reciter;
      _currentSurahIndex = _allSurahs.indexWhere((s) => s.number == surah.number);

      _isPlaying = true;
      _isPaused = false;
      _progress = 0.0;
      _currentPosition = Duration.zero;
      _totalDuration = const Duration(minutes: 5); // Will be updated by actual audio

      print('🎵 AudioProvider: State updated - isPlaying: $_isPlaying');
      notifyListeners();

      // Start actual audio playback
      print('🎵 AudioProvider: Getting AudioService instance...');
      final audioService = AudioService.instance;
      print('🎵 AudioProvider: AudioService instance obtained: ${audioService.runtimeType}');

      print('🎵 AudioProvider: Starting real audio playback...');
      print('🎵 AudioProvider: Calling audioService.playSurah(${surah.number}, ${reciter.arabicName})');

      try {
        await audioService.playSurah(surah.number, reciter);
        print('🎵 AudioProvider: Real audio started successfully');
      } catch (e) {
        print('❌ AudioProvider: Error calling audioService.playSurah: $e');
        print('❌ AudioProvider: Error type: ${e.runtimeType}');
        rethrow;
      }

      // AudioService will handle progress tracking through callbacks

    } catch (e) {
      print('❌ AudioProvider: Error in playSurah: $e');
      _isPlaying = false;
      _isPaused = false;
      notifyListeners();
      rethrow;
    }
  }

  // Pause playback
  void pause() {
    print('⏸️ AudioProvider: Pausing playback');
    _isPlaying = false;
    _isPaused = true;
    notifyListeners();

    // Pause actual audio
    try {
      AudioService.instance.pause();
      print('⏸️ AudioProvider: Real audio paused');
    } catch (e) {
      print('❌ AudioProvider: Error pausing audio: $e');
    }
  }

  // Resume playback
  void resume() {
    print('▶️ AudioProvider: Resuming playback');
    _isPlaying = true;
    _isPaused = false;
    notifyListeners();

    // Resume actual audio
    try {
      AudioService.instance.resume();
      print('▶️ AudioProvider: Real audio resumed');
    } catch (e) {
      print('❌ AudioProvider: Error resuming audio: $e');
    }

    // AudioService will handle progress tracking through callbacks
  }

  // Stop playback
  void stop() {
    print('⏹️ AudioProvider: Stopping playback');
    _isPlaying = false;
    _isPaused = false;
    _progress = 0.0;
    _currentPosition = Duration.zero;
    _totalDuration = Duration.zero;
    _currentSurah = null;
    _currentReciter = null;

    notifyListeners();

    // Stop actual audio
    try {
      AudioService.instance.stop();
      print('⏹️ AudioProvider: Real audio stopped');
    } catch (e) {
      print('❌ AudioProvider: Error stopping audio: $e');
    }
  }

  // Play next surah
  Future<void> playNextSurah() async {
    print('⏭️ AudioProvider: Attempting to play next surah');
    if (canGoNext && _currentReciter != null) {
      final nextSurah = _allSurahs[_currentSurahIndex + 1];
      print('⏭️ AudioProvider: Playing next surah: ${nextSurah.name}');
      await playSurah(nextSurah, _currentReciter!);
    } else {
      print('⏭️ AudioProvider: Cannot go to next surah - canGoNext: $canGoNext, reciter: ${_currentReciter?.arabicName}');
    }
  }

  // Play previous surah
  Future<void> playPreviousSurah() async {
    print('⏮️ AudioProvider: Attempting to play previous surah');
    if (canGoPrevious && _currentReciter != null) {
      final previousSurah = _allSurahs[_currentSurahIndex - 1];
      print('⏮️ AudioProvider: Playing previous surah: ${previousSurah.name}');
      await playSurah(previousSurah, _currentReciter!);
    } else {
      print('⏮️ AudioProvider: Cannot go to previous surah - canGoPrevious: $canGoPrevious, reciter: ${_currentReciter?.arabicName}');
    }
  }

  // Update progress
  void updateProgress(Duration position, Duration total) {
    _currentPosition = position;
    _totalDuration = total;

    if (total.inMilliseconds > 0) {
      _progress = position.inMilliseconds / total.inMilliseconds;
    } else {
      _progress = 0.0;
    }

    notifyListeners();
  }

  // Handle playback completion
  void onPlaybackCompleted() {
    if (canGoNext) {
      // Auto play next surah
      playNextSurah();
    } else {
      // Stop if it's the last surah
      stop();
    }
  }



  // Format duration for display
  String formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}